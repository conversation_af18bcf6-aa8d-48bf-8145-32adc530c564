'use strict';

var optional = require('./optional')
  , tough = optional('tough-cookie')
  , Cook<PERSON> = tough && tough.<PERSON><PERSON>
  , CookieJar = tough && tough.CookieJar
  ;

exports.parse = function(str) {
  if (str && str.uri) str = str.uri
  if (typeof str !== 'string') throw new Error("The cookie function only accepts STRING as param")
  if (!<PERSON>ie) {
    return null;
  }
  return Cookie.parse(str)
};

// Adapt the sometimes-Async api of tough.CookieJar to our requirements
function RequestJar() {
  this._jar = new CookieJar();
}
RequestJar.prototype.setCookie = function(cookieOrStr, uri, options) {
  return this._jar.setCookieSync(cookieOrStr, uri, options || {});
};
RequestJar.prototype.getCookieString = function(uri) {
  return this._jar.getCookieStringSync(uri);
};
RequestJar.prototype.getCookies = function(uri) {
  return this._jar.getCookiesSync(uri);
};

exports.jar = function() {
  if (!CookieJar) {
    // tough-cookie not loaded, return a stub object:
    return {
      setCookie: function(){},
      getCookieString: function(){},
      getCookies: function(){}
    };
  }
  return new RequestJar();
};
