{"name": "async", "description": "Higher-order functions and common patterns for asynchronous code", "main": "lib/async.js", "author": "<PERSON><PERSON>", "version": "0.9.2", "keywords": ["async", "callback", "utility", "module"], "repository": {"type": "git", "url": "https://github.com/caolan/async.git"}, "bugs": {"url": "https://github.com/caolan/async/issues"}, "license": "MIT", "devDependencies": {"nodeunit": ">0.0.0", "uglify-js": "1.2.x", "nodelint": ">0.0.0", "lodash": ">=2.4.1"}, "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "scripts": {"test": "nodeunit test/test-async.js"}, "spm": {"main": "lib/async.js"}, "volo": {"main": "lib/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}}