{"name": "request", "description": "Simplified HTTP request client.", "tags": ["http", "simple", "util", "utility"], "version": "2.45.0", "author": "<PERSON><PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/mikeal/request.git"}, "bugs": {"url": "http://github.com/mikeal/request/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8.0"}, "main": "index.js", "dependencies": {"bl": "~0.9.0", "caseless": "~0.6.0", "forever-agent": "~0.5.0", "qs": "~1.2.0", "json-stringify-safe": "~5.0.0", "mime-types": "~1.0.1", "node-uuid": "~1.4.0", "tunnel-agent": "~0.4.0", "form-data": "~0.1.0"}, "optionalDependencies": {"tough-cookie": ">=0.12.0", "http-signature": "~0.10.0", "oauth-sign": "~0.4.0", "hawk": "1.1.1", "aws-sign2": "~0.5.0", "stringstream": "~0.0.4"}, "scripts": {"test": "npm run lint && node tests/run.js", "lint": "./node_modules/eslint/bin/eslint.js lib/ *.js"}, "devDependencies": {"rimraf": "~2.2.8", "eslint": "0.5.1"}}