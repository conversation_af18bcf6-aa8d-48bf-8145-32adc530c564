{"name": "bl", "version": "0.9.5", "description": "Buffer List: collect buffers and access with a standard readable Buffer interface, streamable too!", "main": "bl.js", "scripts": {"test": "node test/test.js | faucet", "test-local": "brtapsauce-local test/basic-test.js"}, "repository": {"type": "git", "url": "https://github.com/rvagg/bl.git"}, "homepage": "https://github.com/rvagg/bl", "authors": ["<PERSON> Vagg <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/mcollina)", "<PERSON><PERSON><PERSON> <<EMAIL>> (https://github.com/jcrugzz)"], "keywords": ["buffer", "buffers", "stream", "awesomesauce"], "license": "MIT", "dependencies": {"readable-stream": "~1.0.26"}, "devDependencies": {"faucet": "~0.0.1", "hash_file": "~0.1.1", "tape": "~4.4.0"}}