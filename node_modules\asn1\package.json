{"author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>"], "name": "asn1", "description": "Contains parsers and serializers for ASN.1 (currently BER only)", "version": "0.1.11", "repository": {"type": "git", "url": "git://github.com/mcavage/node-asn1.git"}, "main": "lib/index.js", "engines": {"node": ">=0.4.9"}, "dependencies": {}, "devDependencies": {"tap": "0.1.4"}, "scripts": {"pretest": "which gjslint; if [[ \"$?\" = 0 ]] ; then  gjslint --nojsdoc -r lib -r tst; else echo \"Missing gjslint. Skipping lint\"; fi", "test": "./node_modules/.bin/tap ./tst"}}