{"name": "sntp", "description": "SNTP Client", "version": "0.2.4", "author": "<PERSON><PERSON> <<EMAIL>> (http://hueniverse.com)", "contributors": [], "repository": "git://github.com/hueniverse/sntp", "main": "index", "keywords": ["sntp", "ntp", "time"], "engines": {"node": ">=0.8.0"}, "dependencies": {"hoek": "0.9.x"}, "devDependencies": {"lab": "0.1.x", "complexity-report": "0.x.x"}, "scripts": {"test": "make test-cov"}, "licenses": [{"type": "BSD", "url": "http://github.com/hueniverse/sntp/raw/master/LICENSE"}]}