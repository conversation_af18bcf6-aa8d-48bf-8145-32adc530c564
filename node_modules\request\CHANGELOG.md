## Change Log

### upcoming (2014/07/09 12:10 +00:00)
- [#946](https://github.com/mikeal/request/pull/946) defaults: merge headers (@aj0strow)
- [#844](https://github.com/mikeal/request/pull/844) Add support for HTTP[S]_PROXY environment variables.  Fixes #595. (@jvmccarthy)

### v2.37.1 (2014/07/07 17:25 +00:00)
- [8711b2f](https://github.com/mikeal/request/commit/8711b2f3489553a7ddae69fa8c9f538182c9d5c8) 2.37.1 (@mikeal)

### v2.37.0 (2014/07/07 17:25 +00:00)
- [79472b2](https://github.com/mikeal/request/commit/79472b263cde77504a354913a16bdc9fbdc9ed5d) append secureOptions to pool<PERSON>ey (@medovob)
- [#907](https://github.com/mikeal/request/pull/907) append secureOptions to poolKey (@medovob)
- [b223a8a](https://github.com/mikeal/request/commit/b223a8add0cbdd4e699a52da66aeb0f0cb17a0c3) expose tough-cookie's getCookiesSync (@charlespwd)
- [f4dcad0](https://github.com/mikeal/request/commit/f4dcad0fa6e2f2388abae508ad7256a1e1214ab2) test getCookies method (@charlespwd)
- [adcf62b](https://github.com/mikeal/request/commit/adcf62bf45ec19a28198ca8d3f37e7d7babc883a) update readme (@charlespwd)
- [4fdf13b](https://github.com/mikeal/request/commit/4fdf13b57dcd20b9fe03c0956f5df70c82d6e4a3) Merge branch 'charlespwd-master' (@lalitkapoor)
- [83e370d](https://github.com/mikeal/request/commit/83e370d54ca2a5fb162e40e7e705e1e9d702ba0a) Bump version of hawk dep. (@samccone)
- [#927](https://github.com/mikeal/request/pull/927) Bump version of hawk dep. (@samccone)
- [c42dcec](https://github.com/mikeal/request/commit/c42dcec10a307cb2299861f87720d491a89142b4) package.json: use OSI-style license name (@isaacs)
- [8892cb7](https://github.com/mikeal/request/commit/8892cb7bb8945807ff25038e888222d4e902acc8) Swap mime module. (@eiriksm)
- [d92395e](https://github.com/mikeal/request/commit/d92395e638cbfe5c31eb4ff54941b98b09057486) Make package.json so node .8 understands it. (@eiriksm)
- [6ebd748](https://github.com/mikeal/request/commit/6ebd748a02a49976d41ebbc4f8396acf8fda1c14) Add some additional hacks to work in the browser. (@eiriksm)
- [#943](https://github.com/mikeal/request/pull/943) New mime module (@eiriksm)
- [561454d](https://github.com/mikeal/request/commit/561454d18a68b7a03163308f6d29e127afe97426) Add some code comments about why we do the extra checks. (@eiriksm)
- [#944](https://github.com/mikeal/request/pull/944) Make request work with browserify (@eiriksm)
- [6a0add7](https://github.com/mikeal/request/commit/6a0add70b2687cf751b3446a15a513a1fd141738) defaults: merge headers (@aj0strow)
- [407c1ad](https://github.com/mikeal/request/commit/407c1ada61afca4d4ba50155c6d9430754541df1) prefer late return statement (@aj0strow)
- [4ab40ba](https://github.com/mikeal/request/commit/4ab40ba2f9aca8958cab149eb9cfbd9edb5534aa) Added support for manual querystring in form option (@charlespwd)
- [a55627c](https://github.com/mikeal/request/commit/a55627cd9f468cefb2971bb501ebc0c2fc27aa8b) Updated README (@charlespwd)
- [#949](https://github.com/mikeal/request/pull/949) Manually enter querystring in form option (@charlespwd)
- [10246c8](https://github.com/mikeal/request/commit/10246c84819db14b32fccca040029b06449242a3) [PATCH v2] Add support for gzip content decoding (@kevinoid)
- [6180c5f](https://github.com/mikeal/request/commit/6180c5f45c01fb2158b9a44f894a34263479fa84) check for content-length header before setting it in nextTick (@camilleanne)
- [#951](https://github.com/mikeal/request/pull/951) Add support for gzip content decoding (@kevinoid)
- [849c681](https://github.com/mikeal/request/commit/849c681846ce3b5492bd47261de391377a3ac19b) Silence EventEmitter memory leak warning #311 (@watson)
- [#955](https://github.com/mikeal/request/pull/955) check for content-length header before setting it in nextTick (@camilleanne)
- [#957](https://github.com/mikeal/request/pull/957) Silence EventEmitter memory leak warning #311 (@watson)
- [c1d951e](https://github.com/mikeal/request/commit/c1d951e536bd41c957f0cade41d051c9d41d1462) Fixing for 0.8 (@mikeal)
- [4851118](https://github.com/mikeal/request/commit/48511186495888a5f0cb15a107325001ac91990e) 2.37.0 (@mikeal)

### v2.36.1 (2014/05/19 20:59 +00:00)
- [c3914fc](https://github.com/mikeal/request/commit/c3914fcd4a74faf6dbf0fb6a4a188e871e0c51b8) 2.36.1 (@mikeal)

### v2.36.0 (2014/05/19 20:59 +00:00)
- [76a96de](https://github.com/mikeal/request/commit/76a96de75580042aa780e9587ff7a22522119c3f) Reventing lodash merge change. (@mikeal)
- [b8bb57e](https://github.com/mikeal/request/commit/b8bb57efb17e72e2ac6d957c05c3f2570c7ba6a0) 2.36.0 (@mikeal)

### v2.35.1 (2014/05/17 20:57 +00:00)
- [4bbd153](https://github.com/mikeal/request/commit/4bbd1532a68cadf1a88dd69c277645e9b781f364) 2.35.1 (@mikeal)

### v2.35.0 (2014/05/17 20:57 +00:00)
- [2833da3](https://github.com/mikeal/request/commit/2833da3c3c1c34f4130ad1ba470354fc32410691) initial changelog (@lalitkapoor)
- [49319e6](https://github.com/mikeal/request/commit/49319e6c09a8a169c95a8d282c900f9fecd50371) Merge branch 'master' of https://github.com/mikeal/request into create-changelog-based-on-pull-requests (@lalitkapoor)
- [#815](https://github.com/mikeal/request/pull/815) Create changelog based on pull requests (@lalitkapoor)
- [4b6ce1a](https://github.com/mikeal/request/commit/4b6ce1ac0f79cb8fa633e281d3eb4c0cb61794e1) It appears that secureOptions is an undocumented feature to fix issues with broken server. See joynet/node #5119 (@nw)
- [#821](https://github.com/mikeal/request/pull/821) added secureOptions back (@nw)
- [eddd488](https://github.com/mikeal/request/commit/eddd4889fb1bc95c741749e79d9749aab3e103fc) Fixing #825 (@mikeal)
- [4627a7a](https://github.com/mikeal/request/commit/4627a7a14078494ded8c66c19c43efd07324cbd8) improve error reporting for invalid protocols (@FND)
- [#840](https://github.com/mikeal/request/pull/840) improve error reporting for invalid protocols (@FND)
- [#810](https://github.com/mikeal/request/pull/810) add some exposition to mpu example in README.md (@mikermcneil)
- [8a0e2d6](https://github.com/mikeal/request/commit/8a0e2d65351560858275c73505df12b537f4d001) Added support for HTTP_PROXY and HTTPS_PROXY environment variables, if the proxy option isn't already set. (@jvmccarthy)
- [f60d348](https://github.com/mikeal/request/commit/f60d348dc1840ee6d7b709efcc2b3cd1a03aef63) Fix word consistency
- [#850](https://github.com/mikeal/request/pull/850) Fix word consistency in readme (@0xNobody)
- [#809](https://github.com/mikeal/request/pull/809) upgrade tunnel-proxy to 0.4.0 (@ksato9700)
- [e86377c](https://github.com/mikeal/request/commit/e86377c0c1e7695c3997f7802175ca37f5a5113b) Won't use HTTP(S)_PROXY env var if proxy explicitly set to null. (@jvmccarthy)
- [f1bb537](https://github.com/mikeal/request/commit/f1bb537ee2440bd664ea8c445ac3a2c6e31e9932) Add support for RFC 6750 Bearer Tokens
- [ba51a26](https://github.com/mikeal/request/commit/ba51a26079ec52c0a9145fbe8b6796d46e79bb8e) Add documentation about auth.bearer (@phedny)
- [#861](https://github.com/mikeal/request/pull/861) Add support for RFC 6750 Bearer Tokens (@phedny)
- [b8ee579](https://github.com/mikeal/request/commit/b8ee5790ace95440a56074f6afe866f4662e9e88) Fix typo (@dandv)
- [#866](https://github.com/mikeal/request/pull/866) Fix typo (@dandv)
- [b292b59](https://github.com/mikeal/request/commit/b292b59fadecb35dac3bee0959c4b4b782e772e3) Clean code syntax in test-pipes.js (@tgohn)
- [f7996d5](https://github.com/mikeal/request/commit/f7996d5fcfed85e03f293a7c9739e385b64ecaad) Add test for request.pipefilter (@tgohn)
- [#869](https://github.com/mikeal/request/pull/869) Pipefilter test (@tgohn)
- [86b99b6](https://github.com/mikeal/request/commit/86b99b671a3c86f4f963a6c67047343fd8edae8f) Fix typo in form example (@mscdex)
- [2ba4808](https://github.com/mikeal/request/commit/2ba48083ddf2607f85e2c479e0d254483c2610fe) failing test (@lalitkapoor)
- [39396b0](https://github.com/mikeal/request/commit/39396b0bb2e90eb7ec4dfcf5d2e731a2cb156f5c) extend passed in options (@lalitkapoor)
- [#891](https://github.com/mikeal/request/pull/891) fixes 857 - options object is mutated by calling request (@lalitkapoor)
- [54a51c6](https://github.com/mikeal/request/commit/54a51c665887e162ccb9f6b17b9c1f3b017ccc29) merge options (@vohof)
- [25b95db](https://github.com/mikeal/request/commit/25b95dbdddf874f014386a0a9fe35a7c903b7415) tilde? (@vohof)
- [#897](https://github.com/mikeal/request/pull/897) merge with default options (@vohof)
- [a1e4b1a](https://github.com/mikeal/request/commit/a1e4b1a9c2f39ce565fd023bb604da139f689d43) Fixes #555 (@pigulla)
- [#901](https://github.com/mikeal/request/pull/901) Fixes #555 (@pigulla)
- [6498a5f](https://github.com/mikeal/request/commit/6498a5f1ae68050cfeabf8f34f75bc72b08f1805) 2.35.0 (@mikeal)

### v2.34.1 (2014/02/18 19:35 +00:00)
- [aefea20](https://github.com/mikeal/request/commit/aefea20b215ff1a48f0d8d27dcac0186604e3b2d) 2.34.1 (@mikeal)

### v2.34.0 (2014/02/18 19:35 +00:00)
- [46edc90](https://github.com/mikeal/request/commit/46edc902e6ffdee39038a6702021728cb9d9b8fa) simpler (@joaojeronimo)
- [#781](https://github.com/mikeal/request/pull/781) simpler isReadStream function (@joaojeronimo)
- [fe2f59f](https://github.com/mikeal/request/commit/fe2f59fdc72de5c86404e51ab6bc4e0e8ece95f2) Provide ability to override content-type when `json` option used (@vvo)
- [#785](https://github.com/mikeal/request/pull/785) Provide ability to override content-type when `json` option used (@vvo)
- [d134f01](https://github.com/mikeal/request/commit/d134f012e64702e8f4070d61504b39524e1a07ba) Adds content-length calculation when submitting forms using form-data library. This is related to issue 345. (@Juul)
- [#793](https://github.com/mikeal/request/pull/793) Adds content-length calculation when submitting forms using form-data li... (@Juul)
- [3ebf25c](https://github.com/mikeal/request/commit/3ebf25c5af1194d8f7b3a3330fe89e729532809b) adding failing test (@lalitkapoor)
- [0f57a90](https://github.com/mikeal/request/commit/0f57a90384588727a5446bb1f5bf4e0be2d85780) accept options in arguments (@lalitkapoor)
- [7fb1647](https://github.com/mikeal/request/commit/7fb164731a5aad80c6539e33eda4ad4a51bb7871) silently ignore errors when adding cookie to jar (@lalitkapoor)
- [d6b2b1c](https://github.com/mikeal/request/commit/d6b2b1c279d12cdddc6593060672d49b12e63fea) add additional header test (@lalitkapoor)
- [f29e6df](https://github.com/mikeal/request/commit/f29e6dfadc6c3a45b6190998b6608059f87f3c32) Added the Apache license to the package.json. (@keskival)
- [#802](https://github.com/mikeal/request/pull/802) Added the Apache license to the package.json. (@keskival)
- [#801](https://github.com/mikeal/request/pull/801) 794 ignore cookie parsing and domain errors (@lalitkapoor)
- [54e6dfb](https://github.com/mikeal/request/commit/54e6dfb77d57757d4006982f813ebaab9e005cd5) Rewrite UNIX Domain Socket support into 2.33.1. Add test. (@lyuzashi)
- [3eaed2f](https://github.com/mikeal/request/commit/3eaed2f2e82d9d17a583bcc54270c16a7b674206) Use setImmediate when available, otherwise fallback to nextTick (@lyuzashi)
- [746ca75](https://github.com/mikeal/request/commit/746ca757da24d5011e92e04cb00c90098a7680fd) Indent wrapped buildRequest function (@lyuzashi)
- [#516](https://github.com/mikeal/request/pull/516) UNIX Socket URL Support (@native-digital)
- [9a5b0a8](https://github.com/mikeal/request/commit/9a5b0a81eca9836f05b0192c05c0d41e79034461) initial format (@lalitkapoor)
- [9380a49](https://github.com/mikeal/request/commit/9380a49779ddb081eba5d0ee51e4396d72d52066) upgrade tunnel-proxy to 0.4.0 (@ksato9700)
- [1efea37](https://github.com/mikeal/request/commit/1efea374286c728c3c988ee2264fb44cd8c41d88) add some exposition to mpu example in README.md (@mikermcneil)
- [ba0d63a](https://github.com/mikeal/request/commit/ba0d63ae23a3fc95dfe012df0bd6c8d7e87b1df7) made the language clearer (@mikermcneil)
- [b43aa81](https://github.com/mikeal/request/commit/b43aa81789c0b8c7ae90d2b983f79dde4a125470) 2.34.0 (@mikeal)

### v2.33.1 (2014/01/16 19:48 +00:00)
- [afcf827](https://github.com/mikeal/request/commit/afcf827559b3223c96ac1bbd19bd1e4a6d7771e3) 2.33.1 (@mikeal)

### v2.33.0 (2014/01/16 19:48 +00:00)
- [7f1cc8f](https://github.com/mikeal/request/commit/7f1cc8ff5a8d9443e7a793f4655487e722b75b0d) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [3e43d3d](https://github.com/mikeal/request/commit/3e43d3d5175f5f18d1e97b2f5d4ca6ac6c216e4a) 2.33.0 (@mikeal)

### v2.32.1 (2014/01/16 19:33 +00:00)
- [dd44f39](https://github.com/mikeal/request/commit/dd44f39d37daacbbeb21f9e960f13adbb44eea0a) 2.32.1 (@mikeal)

### v2.32.0 (2014/01/16 19:33 +00:00)
- [#757](https://github.com/mikeal/request/pull/757) require aws-sign2 (@mafintosh)
- [#744](https://github.com/mikeal/request/pull/744) Use Cookie.parse (@lalitkapoor)
- [5eaee1c](https://github.com/mikeal/request/commit/5eaee1ce4008ede1df15201622ac478c892d6a8a) Upgrade tough-cookie to 0.10.0 (@stash)
- [#763](https://github.com/mikeal/request/pull/763) Upgrade tough-cookie to 0.10.0 (@stash)
- [d2489d0](https://github.com/mikeal/request/commit/d2489d0e24d9a538224f5c8c090dcdeb1f8d4969) Fixed auth error for some servers like twisted. According to rfc 2617 auth scheme token should be case-insensitive. (@bobyrizov)
- [#764](https://github.com/mikeal/request/pull/764) Case-insensitive authentication scheme (@bobyrizov)
- [cbee3d0](https://github.com/mikeal/request/commit/cbee3d04ee9f704501a64edb7b9b6d201e98494b) Use tough-cookie CookieJar sync API (@stash)
- [3eeaf6a](https://github.com/mikeal/request/commit/3eeaf6a90df7b806d91ae1e8e2f56862ece2ea33) Emit error, not cookieError (@stash)
- [#767](https://github.com/mikeal/request/pull/767) Use tough-cookie CookieJar sync API (@stash)
- [9eac534](https://github.com/mikeal/request/commit/9eac534dd11e40bba65456491cb62ad68d8f41fa) 2.32.0 (@mikeal)

### v2.31.1 (2014/01/08 02:57 +00:00)
- [b1b5e91](https://github.com/mikeal/request/commit/b1b5e9161e149574ba5528c401a70bfadef1a98a) 2.31.1 (@mikeal)

### v2.31.0 (2014/01/08 02:57 +00:00)
- [dd2577f](https://github.com/mikeal/request/commit/dd2577f8264d4d4b07484dec7094b72c00c8416f) Removing s3 test. (@mikeal)
- [fef5bf3](https://github.com/mikeal/request/commit/fef5bf34258e3695b61c048c683f1d4a7f99b368) Fix callback arguments documentation (@mmalecki)
- [#736](https://github.com/mikeal/request/pull/736) Fix callback arguments documentation (@mmalecki)
- [5531c20](https://github.com/mikeal/request/commit/5531c208678145ef35b06e948190be2fd6a8a1c8) updating README example: cookie jar api changed cookie module changed to tough-cookie (@emkay)
- [#741](https://github.com/mikeal/request/pull/741) README example is using old cookie jar api (@emkay)
- [9d73e5a](https://github.com/mikeal/request/commit/9d73e5a277af141a6e4fa9dbcae5d0c3b755d277) add note about JSON output body type (@iansltx)
- [#742](https://github.com/mikeal/request/pull/742) Add note about JSON output body type (@iansltx)
- [41e20a4](https://github.com/mikeal/request/commit/41e20a4d288e30101e493b383a0e4852a3271a98) Use Cookie.parse (@lalitkapoor)
- [4d09556](https://github.com/mikeal/request/commit/4d095562a5c42ffb41b0ff194e9e6f32c0f44372) updating setCookie example to make it clear that the callback is required (@emkay)
- [#745](https://github.com/mikeal/request/pull/745) updating setCookie example to make it clear that the callback is required (@emkay)
- [b7ede1d](https://github.com/mikeal/request/commit/b7ede1d56f9a2764e4bf764687b81419df817e5a) README: Markdown code highlight (@weakish)
- [#746](https://github.com/mikeal/request/pull/746) README: Markdown code highlight (@weakish)
- [#645](https://github.com/mikeal/request/pull/645) update twitter api url to v1.1 (@mick)
- [20dcd18](https://github.com/mikeal/request/commit/20dcd18ce8e3397ba7e0213da9c760b048ca5b49) require aws-sign2 (@mafintosh)
- [df2c426](https://github.com/mikeal/request/commit/df2c4264321c3db1387ddf9a945d63b9ae7d57b8) 2.31.0 (@mikeal)

### v2.30.1 (2013/12/13 19:17 +00:00)
- [eba2d40](https://github.com/mikeal/request/commit/eba2d402fcdcf1ac878de8672b1c9f5da856dcc1) 2.30.1 (@mikeal)

### v2.30.0 (2013/12/13 19:17 +00:00)
- [aee3819](https://github.com/mikeal/request/commit/aee38191557574ef570fd9c764af0af7072cc92a) Fix TypeError when calling request.cookie
- [#728](https://github.com/mikeal/request/pull/728) Fix TypeError when calling request.cookie (@scarletmeow)
- [628ef76](https://github.com/mikeal/request/commit/628ef768b1f52710b8eb4e14be4db69d174d1dcb) better DIGEST support (@dai-shi)
- [d919bc1](https://github.com/mikeal/request/commit/d919bc1ce97fa461c365437a0c739bbaa6b86de7) ignore null authValues (DIGEST) (@dai-shi)
- [75fc209](https://github.com/mikeal/request/commit/75fc209c5a9e6c647a04e42048c30f46c66fc103) DIGEST support: pass algoritm and opaque, add TODO items, test case for compatible mode (@dai-shi)
- [#730](https://github.com/mikeal/request/pull/730) better HTTP DIGEST support (@dai-shi)
- [937a24a](https://github.com/mikeal/request/commit/937a24a168a126f406ee8eb55eb78169ddc53497) JSHINT: Creating global 'for' variable. Should be 'for (var ...'.
- [#732](https://github.com/mikeal/request/pull/732) JSHINT: Creating global 'for' variable. Should be 'for (var ...'. (@Fritz-Lium)
- [f03be23](https://github.com/mikeal/request/commit/f03be2309bd85a89d2e3c208b2fb4be1a2b95c79) Make digest qop regex more robust (see #730) (@nylen)
- [c7d97ae](https://github.com/mikeal/request/commit/c7d97aefaebf773ce62c72e9ec656f0250b7a1e7) 2.30.0 (@mikeal)

### v2.29.1 (2013/12/06 20:05 +00:00)
- [e0f2c41](https://github.com/mikeal/request/commit/e0f2c41bd4e15518e97dd2f4c134be51ed4cb68b) 2.29.1 (@mikeal)

### v2.29.0 (2013/12/06 20:05 +00:00)
- [3c2cad1](https://github.com/mikeal/request/commit/3c2cad11301380f4056eb3ca4c0c124f7f7f72f5) make request.defaults(options, requester) run the requester for all methods (@jchris)
- [#727](https://github.com/mikeal/request/pull/727) fix requester bug (@jchris)
- [0c9f875](https://github.com/mikeal/request/commit/0c9f87542cd1f919751d3ed1f00208ce7705f8e7) 2.29.0 (@mikeal)

### v2.28.1 (2013/12/04 19:42 +00:00)
- [3e6a300](https://github.com/mikeal/request/commit/3e6a300121586da81b871f759a9feec52810474a) 2.28.1 (@mikeal)

### v2.28.0 (2013/12/04 19:42 +00:00)
- [ac26f43](https://github.com/mikeal/request/commit/ac26f43d9a8212289f92056d3029c207f755cef4) Update request.js (@wprl)
- [adc2cb6](https://github.com/mikeal/request/commit/adc2cb6721e5980e8ed667a3f558cce8c89ee6c2) Use random cnonce (@wprl)
- [ff16a9d](https://github.com/mikeal/request/commit/ff16a9daf93e01cecee7fabec64c3e1b423f7db5) Add test for random cnonce (@wprl)
- [df64c2b](https://github.com/mikeal/request/commit/df64c2bc8f691ecc6f6c214e2254bab439830b88) Restore whitespace (@wprl)
- [#630](https://github.com/mikeal/request/pull/630) Send random cnonce for HTTP Digest requests (@wprl)
- [aca5a16](https://github.com/mikeal/request/commit/aca5a169c44cc658e8310691a2ae1cfc4c2b0958) update twitter api url to v1.1 (@mick)
- [abcbadd](https://github.com/mikeal/request/commit/abcbadd1b2a113c34a37b62d36ddcfd74452850e) Test case for #304. (@diversario)
- [b8cf874](https://github.com/mikeal/request/commit/b8cf8743b66d8eee4048561a7d81659f053393c8) fix failure when running with NODE_DEBUG=request, and a test for that (@jrgm)
- [e6c7d1f](https://github.com/mikeal/request/commit/e6c7d1f6d23922480c09427d5f54f84eec60b7af) quiet, but check that stderr output has something reasonable for debug (@jrgm)
- [#659](https://github.com/mikeal/request/pull/659) fix failure when running with NODE_DEBUG=request, and a test for that (@jrgm)
- [23164e4](https://github.com/mikeal/request/commit/23164e4f33bd0837d796037c3d0121db23653c34) option.tunnel to explicitly disable tunneling (@seanmonstar)
- [#662](https://github.com/mikeal/request/pull/662) option.tunnel to explicitly disable tunneling (@seanmonstar)
- [#656](https://github.com/mikeal/request/pull/656) Test case for #304. (@diversario)
- [da16120](https://github.com/mikeal/request/commit/da16120a8f0751b305a341c012dbdcfd62e83585) Change `secureOptions' to `secureProtocol' for HTTPS request (@richarddong)
- [43d9d0a](https://github.com/mikeal/request/commit/43d9d0a76974d2c61681ddee04479d514ebfa320) add `ciphers' and `secureProtocol' to `options' in `getAgent' (@richarddong)
- [#666](https://github.com/mikeal/request/pull/666) make `ciphers` and `secureProtocol` to work in https request (@richarddong)
- [524e035](https://github.com/mikeal/request/commit/524e0356b73240409a11989d369511419526b5ed) change cookie module (@sxyizhiren)
- [#674](https://github.com/mikeal/request/pull/674) change cookie module,to tough-cookie.please check it . (@sxyizhiren)
- [e8dbcc8](https://github.com/mikeal/request/commit/e8dbcc83d4eff3c14e03bd754174e2c5d45f2872) tests: Fixed test-timeout.js events unit test (@Turbo87)
- [aed1c71](https://github.com/mikeal/request/commit/aed1c71fac0047b66a236a990a5569445cfe995d) Added Travis CI configuration file (@Turbo87)
- [#683](https://github.com/mikeal/request/pull/683) Travis CI support (@Turbo87)
- [8bfa640](https://github.com/mikeal/request/commit/8bfa6403ce03cbd3f3de6b82388bfcc314e56c61) dependencies: Set `tough-cookie` as optional dependency (@Turbo87)
- [bcc138d](https://github.com/mikeal/request/commit/bcc138da67b7e1cf29dc7d264a73d8b1d1f4b0e4) dependencies: Set `form-data` as optional dependency (@Turbo87)
- [751ac28](https://github.com/mikeal/request/commit/751ac28b7f13bfeff2a0e920ca2926a005dcb6f0) dependencies: Set `tunnel-agent` as optional dependency (@Turbo87)
- [6d7c1c9](https://github.com/mikeal/request/commit/6d7c1c9d8e3a300ff6f2a93e7f3361799acf716b) dependencies: Set `http-signature` as optional dependency (@Turbo87)
- [733f1e3](https://github.com/mikeal/request/commit/733f1e3ae042a513a18cde1c6e444b18ee07ad66) Added .npmignore file (@Turbo87)
- [e2fc346](https://github.com/mikeal/request/commit/e2fc346b7e5e470fcd36189bcadf63c53feebb22) dependencies: Set `hawk` as optional dependency (@Turbo87)
- [e87d45f](https://github.com/mikeal/request/commit/e87d45fe89ea220035bf07696a70292763f7135f) dependencies: Set `aws-sign` as optional dependency (@Turbo87)
- [1cd81ba](https://github.com/mikeal/request/commit/1cd81ba30908b77cff2fa618aeb232fefaa53ada) lib: Added optional() function (@Turbo87)
- [28c2c38](https://github.com/mikeal/request/commit/28c2c3820feab0cc719df213a60838db019f3e1a) dependencies: Set `oauth-sign` as optional dependency (@Turbo87)
- [2ceddf7](https://github.com/mikeal/request/commit/2ceddf7e793feb99c5b6a76998efe238965b22cd) TravisCI: Test with and without optional dependencies (@Turbo87)
- [#682](https://github.com/mikeal/request/pull/682) Optional dependencies (@Turbo87)
- [2afab5b](https://github.com/mikeal/request/commit/2afab5b665a2e03becbc4a42ad481bb737405655) Handle blank password in basic auth. (@diversario)
- [cabe5a6](https://github.com/mikeal/request/commit/cabe5a62dc71282ce8725672184efe9d97ba79a5) Handle `auth.password` and `auth.username`. (@diversario)
- [#690](https://github.com/mikeal/request/pull/690) Handle blank password in basic auth. (@diversario)
- [33100c3](https://github.com/mikeal/request/commit/33100c3c7fa678f592374f7b2526fe9a0499b6f6) Typo (@VRMink)
- [#694](https://github.com/mikeal/request/pull/694) Typo in README (@ExxKA)
- [9072ff1](https://github.com/mikeal/request/commit/9072ff1556bcb002772838a94e1541585ef68f02) Edited README.md for formatting and clarity of phrasing (@Zearin)
- [#696](https://github.com/mikeal/request/pull/696) Edited README.md for formatting and clarity of phrasing (@Zearin)
- [07ee58d](https://github.com/mikeal/request/commit/07ee58d3a8145740ba34cc724f123518e4b3d1c3) Fixing listing in callback part of docs. (@lukasz-zak)
- [#710](https://github.com/mikeal/request/pull/710) Fixing listing in callback part of docs. (@lukasz-zak)
- [8ee21d0](https://github.com/mikeal/request/commit/8ee21d0dcc637090f98251eba22b9f4fd1602f0e) Request.multipart no longer crashes when header 'Content-type' is present (@pastaclub)
- [#715](https://github.com/mikeal/request/pull/715) Request.multipart no longer crashes when header 'Content-type' present (@pastaclub)
- [8b04ca6](https://github.com/mikeal/request/commit/8b04ca6ad8d025c275e40b806a69112ac53bd416) doc: Removed use of gendered pronouns (@oztu)
- [#719](https://github.com/mikeal/request/pull/719) Made a comment gender neutral. (@oztu)
- [8795fc6](https://github.com/mikeal/request/commit/8795fc68cce26b9a45d10db9eaffd4bc943aca3a) README.md: add custom HTTP Headers example. (@tcort)
- [#724](https://github.com/mikeal/request/pull/724) README.md: add custom HTTP Headers example. (@tcort)
- [c5d5b1f](https://github.com/mikeal/request/commit/c5d5b1fcf348e768943fe632a9a313d704d35c65) Changing dep. (@mikeal)
- [bf04163](https://github.com/mikeal/request/commit/bf04163883fa9c62d4e1a9fdd64d6efd7723d5f8) 2.28.0 (@mikeal)

### v2.27.1 (2013/08/15 21:30 +00:00)
- [a80a026](https://github.com/mikeal/request/commit/a80a026e362a9462d6948adc1b0d2831432147d2) 2.27.1 (@mikeal)

### v2.27.0 (2013/08/15 21:30 +00:00)
- [3627b9c](https://github.com/mikeal/request/commit/3627b9cc7752cfe57ac609ed613509ff61017045) rename Request and remove .DS_Store (@joaojeronimo)
- [920f9b8](https://github.com/mikeal/request/commit/920f9b88f7dd8f8d153e72371b1bf2d16d5e4160) rename Request (@joaojeronimo)
- [c243cc6](https://github.com/mikeal/request/commit/c243cc66131216bb57bcc0fd79c250a7927ee424) for some reason it removed request.js (@joaojeronimo)
- [#619](https://github.com/mikeal/request/pull/619) decouple things a bit (@CrowdProcess)
- [ed4ecc5](https://github.com/mikeal/request/commit/ed4ecc5ae5cd1d9559a937e84638c9234244878b) Try normal stringify first, then fall back to safe stringify (@mikeal)
- [5642ff5](https://github.com/mikeal/request/commit/5642ff56e64c19e8183dcd5b6f9d07cca295a79e) 2.27.0 (@mikeal)

### v2.26.1 (2013/08/07 16:31 +00:00)
- [b422510](https://github.com/mikeal/request/commit/b422510ba16315c3e0e1293a17f3a8fa7a653a77) 2.26.1 (@mikeal)

### v2.26.0 (2013/08/07 16:31 +00:00)
- [3b5b62c](https://github.com/mikeal/request/commit/3b5b62cdd4f3b92e63a65d3a7265f5a85b11c4c9) Only include :password in Basic Auth if it's defined (fixes #602) (@bendrucker)
- [#605](https://github.com/mikeal/request/pull/605) Only include ":" + pass in Basic Auth if it's defined (fixes #602) (@bendrucker)
- [cce2c2c](https://github.com/mikeal/request/commit/cce2c2c8ea5b0136932b2432e4e25c0124d58d5a) Moved init of self.uri.pathname (@lexander)
- [08793ec](https://github.com/mikeal/request/commit/08793ec2f266ef88fbe6c947e6b334e04d4b9dc9) Fix all header casing issues forever. (@mikeal)
- [#613](https://github.com/mikeal/request/pull/613) Fixes #583, moved initialization of self.uri.pathname (@lexander)
- [f98ff99](https://github.com/mikeal/request/commit/f98ff990d294165498c9fbf79b2de12722e5c842) Update this old ass readme with some new HOTNESS! (@mikeal)
- [3312010](https://github.com/mikeal/request/commit/3312010f72d035f22b87a6d8d463f0d91b88fea1) markdown badge instead. (@mikeal)
- [9cf657c](https://github.com/mikeal/request/commit/9cf657c1f08bf460911b8bb0a8c5c0d3ae6135c7) Shorter title. (@mikeal)
- [2c61d66](https://github.com/mikeal/request/commit/2c61d66f1dc323bb612729c7320797b79b22034c) put Request out (@joaojeronimo)
- [28513a1](https://github.com/mikeal/request/commit/28513a1b371452699438c0eb73471f8969146264) 2.26.0 (@mikeal)

### v2.25.1 (2013/07/23 21:51 +00:00)
- [6387b21](https://github.com/mikeal/request/commit/6387b21a9fb2e16ee4dd2ab73b757eca298587b5) 2.25.1 (@mikeal)

### v2.25.0 (2013/07/23 21:51 +00:00)
- [828f12a](https://github.com/mikeal/request/commit/828f12a1ae0f187deee4d531b2eaf7531169aaf2) 2.25.0 (@mikeal)

### v2.24.1 (2013/07/23 20:51 +00:00)
- [29ae1bc](https://github.com/mikeal/request/commit/29ae1bc454c03216beeea69d65b538ce4f61e8c1) 2.24.1 (@mikeal)

### v2.24.0 (2013/07/23 20:51 +00:00)
- [f667318](https://github.com/mikeal/request/commit/f66731870d5f3e0e5655cd89612049b540c34714) Fixed a small typo (@michalstanko)
- [#601](https://github.com/mikeal/request/pull/601) Fixed a small typo (@michalstanko)
- [#594](https://github.com/mikeal/request/pull/594) Emit complete event when there is no callback (@RomainLK)
- [#596](https://github.com/mikeal/request/pull/596) Global agent is being used when pool is specified (@Cauldrath)
- [41ce492](https://github.com/mikeal/request/commit/41ce4926fb08242f19135fd3ae10b18991bc3ee0) New deps. (@mikeal)
- [8176c94](https://github.com/mikeal/request/commit/8176c94d5d17bd14ef4bfe459fbfe9cee5cbcc6f) 2.24.0 (@mikeal)

### v2.23.1 (2013/07/23 02:45 +00:00)
- [63f31cb](https://github.com/mikeal/request/commit/63f31cb1d170a4af498fbdd7566f867423caf8e3) 2.23.1 (@mikeal)

### v2.23.0 (2013/07/23 02:44 +00:00)
- [758f598](https://github.com/mikeal/request/commit/758f598de8d6024db3fa8ee7d0a1fc3e45c50f53) Initial commit. Request package. (@mikeal)
- [104cc94](https://github.com/mikeal/request/commit/104cc94839d4b71aaf3681142daefba7ace78c94) Removing unnecessary markup. (@mikeal)
- [12a4cb8](https://github.com/mikeal/request/commit/12a4cb88b949cb4a81d51189d432c25c08522a87) Matching node documentation style. (@mikeal)
- [ab96993](https://github.com/mikeal/request/commit/ab969931106b10b5f8658dc9e0f512c5dfc2a7da) Release tarball. (@mikeal)
- [e7e37ad](https://github.com/mikeal/request/commit/e7e37ad537081a040ea3e527aac23ae859b40b2c) Removing old tarball. (@mikeal)
- [e66e90d](https://github.com/mikeal/request/commit/e66e90dd814ae7bfbcd52003609d7bde9eafea57) Adding automatic redirect following. (@mikeal)
- [2fc5b84](https://github.com/mikeal/request/commit/2fc5b84832ae42f6ddb081b1909d0a6ca00c8d51) Adding SSL support. (@mikeal)
- [a3ac375](https://github.com/mikeal/request/commit/a3ac375d4b5800a038ae26233425fadc26866fbc) Fixing bug where callback fired for every redirect. (@mikeal)
- [1139efe](https://github.com/mikeal/request/commit/1139efedb5aad4a328c1d8ff45fe77839a69169f) Cleaning up tests. (@mikeal)
- [bb49fe6](https://github.com/mikeal/request/commit/bb49fe6709fa06257f4b7aadc2e450fd45a41328) Rolling version. (@mikeal)
- [4ff3493](https://github.com/mikeal/request/commit/4ff349371931ec837339aa9082c4ac7ddd4c7c35) Updates to README.md (@mikeal)
- [1c9cf71](https://github.com/mikeal/request/commit/1c9cf719c92b02ba85c4e47bd2b92a3303cbe1cf) Adding optional body buffer. (@mikeal)
- [49dfef4](https://github.com/mikeal/request/commit/49dfef42630c4fda6fb208534c00638dc0f06a6b) Rolling version. (@mikeal)
- [ab40cc8](https://github.com/mikeal/request/commit/ab40cc850652e325fcc3b0a44ee7303ae0a7b77f) Preserve the original full path. (@mikeal)
- [6d70f62](https://github.com/mikeal/request/commit/6d70f62c356f18098ca738b3dbedcf212ac3d8d8) Rolling version. (@mikeal)
- [e2ca15a](https://github.com/mikeal/request/commit/e2ca15a0f7e986e3063977ee9bd2eb69e86bdb1f) Fixing bugs and rolling version. (@mikeal)
- [8165254](https://github.com/mikeal/request/commit/81652543d3a09553cbf33095a7932dec53ccecc2) Cleanup. Fixing '' === '/' path bug. (@mikeal)
- [a0536a4](https://github.com/mikeal/request/commit/a0536a46d0b91e204fbde1e4341461bc827c9542) Rolling version. (@mikeal)
- [9ccaad7](https://github.com/mikeal/request/commit/9ccaad7dce05e5dcc3eacaf1500404622a0d8067) Adding stream support for request and response bodies. (@mikeal)
- [585166d](https://github.com/mikeal/request/commit/585166d979d4476e460e9835cc0516d04a9a3e11) Rolling version. (@mikeal)
- [41111c8](https://github.com/mikeal/request/commit/41111c88d711da80ea123df238d62038b89769bf) Bugfix release for response stream. (@mikeal)
- [86e375d](https://github.com/mikeal/request/commit/86e375d093700affe4d6d2b76a7acedbe8da140c) Remove host header when we add it. (@mikeal)
- [3a6277c](https://github.com/mikeal/request/commit/3a6277c81cfd3457c760f2aaea44852ef832a1e8) Rolling version. (@mikeal)
- [7a11f69](https://github.com/mikeal/request/commit/7a11f69d5353ecc1319e2e91ca4aefbaf0338136) writing requestBodyStream into request (@beanieboi)
- [186e9cf](https://github.com/mikeal/request/commit/186e9cf692511d768f8016d311609a0a0a315af6) Using sys.pump (@mikeal)
- [09e7ade](https://github.com/mikeal/request/commit/09e7ade541e1d40316a3f153128871a353e707b1) Fixing host port addition. Rolling version. (@mikeal)
- [cec3f3f](https://github.com/mikeal/request/commit/cec3f3f619322f27e2a82c7fd8971722f98d04d6) Using builtin base64. (@mikeal)
- [2a2e2a2](https://github.com/mikeal/request/commit/2a2e2a2f5c4760d4da3caa1a0f2d14c31a4222dc) new structure. new convenience methods (@mikeal)
- [f835b5f](https://github.com/mikeal/request/commit/f835b5fb605506b8ecd3c17bebe9ed54f0066cfc) removing old files. (@mikeal)
- [91616c4](https://github.com/mikeal/request/commit/91616c4e4f488f75a8b04b5b6f0ceef7e814cffd) Adding better redirect handling. (@mikeal)
- [3a95433](https://github.com/mikeal/request/commit/3a95433cbec9693a16ff365148489a058720ae7c) Fixing tests. (@mikeal)
- [38eb1d2](https://github.com/mikeal/request/commit/38eb1d2fa8dea582bb7c3fb37a7b05ff91857a46) By popular demand, proxy support! Not really tested yet but it seems to kinda work. (@mikeal)
- [45d41df](https://github.com/mikeal/request/commit/45d41dff63f36b25b3403e59c8b172b7aa9ed373) Added proxy auth. (@mikeal)
- [85e3d97](https://github.com/mikeal/request/commit/85e3d97e0dced39a3769c4e3f2707ba3aaab1eaa) Fixing for non-proxy case. (@mikeal)
- [f796da7](https://github.com/mikeal/request/commit/f796da74849d2b0732bd1bae1d2dcaf1243142c1) Fixing relative uri's for forwards. (@mikeal)
- [dead30e](https://github.com/mikeal/request/commit/dead30ebef9c3ff806b895e2bd32f52ba3988c69) Adding support for specifying an encoding for the response body. (@mikeal)
- [9433344](https://github.com/mikeal/request/commit/943334488dcc8e7f90727b86f9eb1bc502c33b4f) Removing debugging statement (@mikeal)
- [41efb7a](https://github.com/mikeal/request/commit/41efb7a7dcca3b47e97c23c6cdbd3e860d3bd82b) Error on maxRedirects exceeded. (@mikeal)
- [9549570](https://github.com/mikeal/request/commit/95495701fa4e99a3ab85acdab71ecdaabe0dbd45) Allow options.url, people do it all the time, might as well just support it. (@mikeal)
- [21a53c0](https://github.com/mikeal/request/commit/21a53c016edcc113e809219639807b46d29dba36) Pumping version. (@mikeal)
- [aca9782](https://github.com/mikeal/request/commit/aca9782285fe1d727570fe8d799561f45d49048e) Fixing byteLength !== string lenght issues. (@mikeal)
- [a77c296](https://github.com/mikeal/request/commit/a77c296431eda2a211f59bdb88654c4a64ed4ef3) Don't rely on automatic semicolon insertion (pretty please :) (@papandreou)
- [8b02f29](https://github.com/mikeal/request/commit/8b02f29c9019dd1d1dd291dd85889b26f592a137) Also set content-length when options.body is the empty string. (@papandreou)
- [023281c](https://github.com/mikeal/request/commit/023281ca9b4414a9bc0170c2b08aaf886a7a08f7) Simplified boolean logic. (@papandreou)
- [4f897fd](https://github.com/mikeal/request/commit/4f897fdd6c7c93bea73dbf34623f09af63bb1ed4) Simplified check for whether response.headers.location starts with "http:" or "https:". (@papandreou)
- [6d7db85](https://github.com/mikeal/request/commit/6d7db85cadf401dffdec07a4d66822207898c69e) Fixed double var declaration. (@papandreou)
- [97255cf](https://github.com/mikeal/request/commit/97255cfd2a4aa8f34d307e7cd96fe1c1f13cb26a) Process redirects as soon as the response arrives. Prevents the uninteresting redirect response from being pumped into responseBodyStream. (@papandreou)
- [b2af15f](https://github.com/mikeal/request/commit/b2af15f4fcbe1115cf8b53c5ae89fbf2365bfffc) New feature: If options.noBuffer is true, don't buffer up the response, just return it. Most of the time getting a readable stream is much more flexible than having the option to pipe the response into a writable stream. For one thing, the stream can be paused. (@papandreou)
- [fee5f89](https://github.com/mikeal/request/commit/fee5f89159a8f36b25df509c55093bf7ebd1c993) A few fixes/changes from papandreou's code, also added new semantics for onResponse. (@mikeal)
- [fa72fcb](https://github.com/mikeal/request/commit/fa72fcb950029b222f0621e2d49304e35d08c380) Updated documentation. (@mikeal)
- [4fc7209](https://github.com/mikeal/request/commit/4fc72098e7eeb9518951b9306115340ffdcce7ce) Fix for both onResponse and callback (@mikeal)
- [3153436](https://github.com/mikeal/request/commit/3153436404fca865a65649d46eb22d9797128c9d) Adding license information. (@mikeal)
- [59570de](https://github.com/mikeal/request/commit/59570dec37913c7e530303a83f03781d9aca958c) Fix for unescaping passwords for basic auth. (@notmatt)
- [0d771ab](https://github.com/mikeal/request/commit/0d771ab7882b97d776179972c51c59386f91b953) require querystring (@notmatt)
- [875f79b](https://github.com/mikeal/request/commit/875f79b6a40340457fafafdadac813cfa5343689) Allow request's body to be an object. (@Stanley)
- [86895b9](https://github.com/mikeal/request/commit/86895b9c37f7b412b7df963c2a75361ff402d8c5) Merge branch 'master' of github.com:Stanley/request (@Stanley)
- [4c9c984](https://github.com/mikeal/request/commit/4c9c984cb37bfd4e901ce24b0e9b283604c27bf4) Better tests. (@mikeal)
- [02f6b38](https://github.com/mikeal/request/commit/02f6b38c1697a55ed43940d1fd0bef6225d4faa2) Added specs for body option (@Stanley)
- [af66607](https://github.com/mikeal/request/commit/af666072a22b8df4d75fe71885139059f56ea5ee) Made specs pass (@Stanley)
- [641ec05](https://github.com/mikeal/request/commit/641ec052dd95797816e781b2c3ac2524841db7cb) Merge branch 'master' of https://github.com/Stanley/request into jsonbody (@mikeal)
- [ab4c96b](https://github.com/mikeal/request/commit/ab4c96be1c002c10806d967a4b266543f8b0267c) Moved spec tests to normal node script tests. Style changes to code and docs. (@mikeal)
- [fc2a7ef](https://github.com/mikeal/request/commit/fc2a7ef301c1266938a5aeb539e4f3fc3b5191dd) Clearer wording for json option. (@mikeal)
- [01371d7](https://github.com/mikeal/request/commit/01371d728082e22aabeb840da82a30aec62d7d8a) Removing specs loader. (@mikeal)
- [560dadd](https://github.com/mikeal/request/commit/560dadd6cbd293622c66cd82b5506704c9850b13) Adding newline to end of test files, makes for cleaner diffs in the future. (@mikeal)
- [a0348dd](https://github.com/mikeal/request/commit/a0348dd0fef462c3c678a639619c27101c757035) Add pass message when tests finish. (@mikeal)
- [da77a0e](https://github.com/mikeal/request/commit/da77a0e152c1dd43f5c1e698110d23e4d32280db) Adding better debug message on failures for GET tests. (@mikeal)
- [6aade82](https://github.com/mikeal/request/commit/6aade822a90724a47176771d137e30b0a702e7ef) throw on error. (@mikeal)
- [4f41b8d](https://github.com/mikeal/request/commit/4f41b8dbbf9a93c53d5ccdf483c9d7803e279916) Rolling version. (@mikeal)
- [7cf01f0](https://github.com/mikeal/request/commit/7cf01f0481afb367b5d0d4878645ac535cfe9a2e) master is moving to node v0.3.6+ (@mikeal)
- [cb403a4](https://github.com/mikeal/request/commit/cb403a4cfdbe3d98feb9151fdbdae1e1436e59ab) Initial support for 0.3.6+.\n\nExperimental support for Request objects as streams. It's untested and requires a pending patch to node.js (@mikeal)
- [a3c80f9](https://github.com/mikeal/request/commit/a3c80f98f42f25d4cb02d5d9e34ba0e67cc89293) Adding defaults call. (@mikeal)
- [55f22f9](https://github.com/mikeal/request/commit/55f22f96365c57aa8687de951e3f9ed982eba408) Request will keep it's own agent pool so that it can expose a maxSockets setting for easy pool sizing. (@mikeal)
- [004741c](https://github.com/mikeal/request/commit/004741c23dc0eaf61f111161bb913ba418e033e4) Fixing reference error. (@mikeal)
- [8548541](https://github.com/mikeal/request/commit/85485414150fbac58b08126b3684f81dcb930bf1) Simplified pool implementation. (@mikeal)
- [9121c47](https://github.com/mikeal/request/commit/9121c47e4cbe47bccc20a75e0e6c6c098dce04fb) Default to globalPool. (@mikeal)
- [9ec3490](https://github.com/mikeal/request/commit/9ec3490aefd52f05b57e6db13730ace54b4439d1) Support for https. Requires pending patch in node core for consistent Agent API. (@mikeal)
- [146b154](https://github.com/mikeal/request/commit/146b154a1a31ae7a30aa9f28e891e4824af548fa) Fixes for reference errors. (@mikeal)
- [8756120](https://github.com/mikeal/request/commit/8756120f83ceb94f8ba600acba274ba512696eef) Only create an agent when a relevant option is passed. (@mikeal)
- [cc3cf03](https://github.com/mikeal/request/commit/cc3cf0322847982875ff32a7cef25c39c29630ba) New HTTP client doesn't require such explicit error listener management. (@mikeal)
- [f7c0379](https://github.com/mikeal/request/commit/f7c0379b99ac7989df7f934be67cc3ae979591bb) Fixing bug in .pipe() handling. Thanks tanepiper. (@mikeal)
- [897a7ef](https://github.com/mikeal/request/commit/897a7ef020cefcb7a36c04a11e286238df8ecdaa) Fixes for streams, docs, and convenience methods. (@mikeal)
- [7c2899a](https://github.com/mikeal/request/commit/7c2899a046b750eda495b23b2d58604260deddbc) Doc fixes. (@mikeal)
- [f535fe1](https://github.com/mikeal/request/commit/f535fe1008c8f11bb37e16f95fe287ed93343704) Doc fixes. (@mikeal)
- [d1deb5b](https://github.com/mikeal/request/commit/d1deb5b4dda4474fe9d480ad42ace664d89e73ee) Pipe tests, all passing! (@mikeal)
- [d67a041](https://github.com/mikeal/request/commit/d67a041783df8d724662d82f9fb792db1be3f4f0) Moving basic example to the top. (@mikeal)
- [6a98b9e](https://github.com/mikeal/request/commit/6a98b9e4a561b516b14d325c48785a9d6f40c514) Do not mix encoding option with pipeing. (@mikeal)
- [06b67ef](https://github.com/mikeal/request/commit/06b67ef01f73572a6a9b586854d4c21be427bdb2) Disable pooling with {pool:false} (@mikeal)
- [1c24881](https://github.com/mikeal/request/commit/1c248815b5dfffda43541e367bd4d66955ca0325) Send all arguments passed to stream methods. (@mikeal)
- [7946393](https://github.com/mikeal/request/commit/7946393893e75df24b390b7ab19eb5b9d6c23891) Better errors and warnings for different pipe conditions. (@mikeal)
- [ee2108d](https://github.com/mikeal/request/commit/ee2108db592113a0fe3840c361277fdd89f0c89c) Removing commented out legacy code. (@mikeal)
- [5f838b3](https://github.com/mikeal/request/commit/5f838b3582eda465f366d7df89c6dd69920405f2) Fixing redirect issue, thanks @linus (@mikeal)
- [c08758e](https://github.com/mikeal/request/commit/c08758e25290ee12278b3eb95d502645e0d66e4e) Adding del alias, thanks tanepiper. (@mikeal)
- [0b7d675](https://github.com/mikeal/request/commit/0b7d6756c120ebf17ce6c70fc1ff4ecd6850e704) Keep require('https') from throwing if node is compiled with --without-ssl. This will still throw for Invalid Protocol if https is used. Which makes more sense and makes request work without SSl support. (@davglass)
- [02fc9f7](https://github.com/mikeal/request/commit/02fc9f7cc8912402a5a98ddefaffa5f6da870562) Rolling version. Pushed new version to npm. (@mikeal)
- [0b30532](https://github.com/mikeal/request/commit/0b30532ee1a3cabb177017acfa7885b157031df2) Sent a patch today to fix this in core but this hack will fix node that predates that fix to core. (@mikeal)
- [5d5d8f4](https://github.com/mikeal/request/commit/5d5d8f43156b04fd3ceb312cfdf47cc2b0c4104d) Rolling version. Pushed new version to npm. (@mikeal)
- [1c00080](https://github.com/mikeal/request/commit/1c000809f1795d2e21635a626cf730aba2049d3e) Fixing reference to tls. (@mikeal)
- [4c355d1](https://github.com/mikeal/request/commit/4c355d1f87fced167e4b21770bfe6f8208f32b53) Be a better stream. (@mikeal)
- [9bed22f](https://github.com/mikeal/request/commit/9bed22f22e007201d4faeebdb486603c3bb088c3) Rolled version and pushed to npm (@mikeal)
- [34df8e2](https://github.com/mikeal/request/commit/34df8e2301dcfd10705b9ff3b257741b0816c8a1) typo in `request.defaults` (@clement)
- [4d7a6d4](https://github.com/mikeal/request/commit/4d7a6d46fa481e43fe873b8c8fad2f7dd816dbb5) default value only if undefined in `request.defaults` + misplaced `return` statement (@clement)
- [243a565](https://github.com/mikeal/request/commit/243a56563f1014318a467e46113b2c61b485f377) Adding support for request(url) (@mikeal)
- [83a9cec](https://github.com/mikeal/request/commit/83a9cec3cb2f7a43a1e10c13da8d0dd72b937965) Fixing case where + is in user or password. (@mikeal)
- [8bb7f98](https://github.com/mikeal/request/commit/8bb7f98ba8b78c217552c979811c07f1299318fe) making Request a duplex stream rather than adding special handling for pipes out. (@mikeal)
- [55a1fde](https://github.com/mikeal/request/commit/55a1fdedcad1e291502ce10010dda7e478a1b503) pause and resume should act on response instead of request (@tobowers)
- [63125a3](https://github.com/mikeal/request/commit/63125a33523e72e449ceef76da57b63522998282) Making request really smart about pipeing to itself so that we can do simple proxy cats (@mikeal)
- [2f9e257](https://github.com/mikeal/request/commit/2f9e257bc39eb329eec660c6d675fb40172fc5a5) Rolling version since master right now has some pretty hot new code in it. (@mikeal)
- [#31](https://github.com/mikeal/request/pull/31) Error on piping a request to a destination (@tobowers)
- [b1f3d54](https://github.com/mikeal/request/commit/b1f3d5439d24b848b2bf3a6459eea74cb0e43df3) The "end" event that was supposed to be emitted to fix a core bug in NodeJS wasn't fired because it wasn't emitted on the response object. (@voxpelli)
- [#35](https://github.com/mikeal/request/pull/35) The "end" event isn't emitted for some responses (@voxpelli)
- [40b1c67](https://github.com/mikeal/request/commit/40b1c676e1d3a292719ad2dd9cf9354c101bad47) Rolling version. (@mikeal)
- [9a28022](https://github.com/mikeal/request/commit/9a28022d0e438d0028e61a53e897689470025e50) Fixing bug in forwarding with new pipes logic. (@mikeal)
- [44e4e56](https://github.com/mikeal/request/commit/44e4e5605b0a9e02036393bcbd3a8d91280f5611) Fixing big bug in forwarding logic. (@mikeal)
- [b0cff72](https://github.com/mikeal/request/commit/b0cff72d63689d96e0b1d49a8a5aef9ccc71cb8b) Added timeout option to abort the request before the response starts responding (@mbrevoort)
- [cc76b10](https://github.com/mikeal/request/commit/cc76b109590437bfae54116e3424b2c6e44a3b3e) corrected spelling error in README (@mbrevoort)
- [#45](https://github.com/mikeal/request/pull/45) Added timeout option (@mbrevoort)
- [1cca56b](https://github.com/mikeal/request/commit/1cca56b29bb670c53d5995e76c0b075a747b5ad7) Fixing for node http client refactor. (@mikeal)
- [2a78aa3](https://github.com/mikeal/request/commit/2a78aa3f827e76c548e001fa519448b24466b518) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [ce12273](https://github.com/mikeal/request/commit/ce12273d3990c1446d3166bbd9e35c0e2435f137) New fs.ReadStream handling hotness. (@mikeal)
- [535e30a](https://github.com/mikeal/request/commit/535e30a4bd4a8e41d97ffa6a4e99630ac09a4bcb) Adding pipe support to HTTP ServerResponse objects. (@mikeal)
- [2f0cf6b](https://github.com/mikeal/request/commit/2f0cf6bf44edbaec4c0a0cb15a679302de7f0aff) Setting proper statusCode. (@mikeal)
- [6e3ecb1](https://github.com/mikeal/request/commit/6e3ecb106c3a32101d80ac0f87968fddd3ac5e2c) Adding test for pipeing file to disc. (@mikeal)
- [bbbb52e](https://github.com/mikeal/request/commit/bbbb52e406b65100b557caa3687a1aa04fab6ff3) Pumping version. (@mikeal)
- [a10b6e4](https://github.com/mikeal/request/commit/a10b6e4c08478364b8079801fdb23f3530fcc85f) Adding reference to Request instance on response to make it easier on inline callbacks. fixes #43. (@mikeal)
- [b9aff1f](https://github.com/mikeal/request/commit/b9aff1fe007dab3f93e666f047fa03a4e8f5f8b7) Add body property to resp when we have it as a shorthand. fixes #28 (@mikeal)
- [411b30d](https://github.com/mikeal/request/commit/411b30dab1fe5b20880113aa801a2fdbb7c35c40) If the error is handled and not throw we would still process redirects. Fixes #34. (@mikeal)
- [8f3c2b4](https://github.com/mikeal/request/commit/8f3c2b4f6dee8838f30e2430a23d5071128148f0) w00t! request 2.0 (@mikeal)
- [9957542](https://github.com/mikeal/request/commit/9957542cc6928443f3a7769510673665b5a90040) valid semver. (@mikeal)
- [31f5ee2](https://github.com/mikeal/request/commit/31f5ee28726ac7e14355cad0c6d2785f9ca422c6) Drastically improved header handling. (@mikeal)
- [c99b8fc](https://github.com/mikeal/request/commit/c99b8fcd706ae035f6248669b017ac2995e45f31) Return destination stream from pipe(). (@mikeal)
- [cba588c](https://github.com/mikeal/request/commit/cba588cec1e204d70f40f8bd11df0e27dc78ef0c) Style fixes. Bye Bye semi-colons. Mostly lined up with npm style. (@mikeal)
- [8515a51](https://github.com/mikeal/request/commit/8515a510ccc0a661d7c28fce6e513a7d71be7f8f) Clearer spacing. Slightly more consistent. (@mikeal)
- [3acd82a](https://github.com/mikeal/request/commit/3acd82a10e7d973fc5dbaa574c2e8906e48e1ee9) add failing test for issue #51 (@benatkin)
- [68c17f6](https://github.com/mikeal/request/commit/68c17f6c9a3d7217368b3b8bc61203e6a14eb4f0) implement parsing json response when json is truthy (@benatkin)
- [1cb1ec1](https://github.com/mikeal/request/commit/1cb1ec114b03394a0a530f245a857d8424cad02d) allow empty string (@benatkin)
- [4f8d2df](https://github.com/mikeal/request/commit/4f8d2df9f845690667a56e7698dbaf23b5028177) support JSON APIs that don't set the write content type (@benatkin)
- [#53](https://github.com/mikeal/request/pull/53) Parse json: Issue #51 (@benatkin)
- [c63e6e9](https://github.com/mikeal/request/commit/c63e6e96378a2b050bddbe1b39337662f304dc95) Adding proxy to docs, don't know why this wasn't already in. (@mikeal)
- [ef767d1](https://github.com/mikeal/request/commit/ef767d12f13a9c78d3df89add7556f5421204843) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [1b12d3a](https://github.com/mikeal/request/commit/1b12d3a9f48a6142d75fa1790c80eb313388ca44) Emit a proper error. (@mikeal)
- [47314d7](https://github.com/mikeal/request/commit/47314d7cb41fe9c3a7717a502bed9cf1b6074ffc) Greatly expanded documentation. (@mikeal)
- [e477369](https://github.com/mikeal/request/commit/e477369b4bbc271248ee8b686c556567570a6cca) Doc refinements. (@mikeal)
- [fe4d221](https://github.com/mikeal/request/commit/fe4d22109bc1411c29b253756d609856327ff146) Fix for newer npm (@mikeal)
- [7b2f788](https://github.com/mikeal/request/commit/7b2f788293e205edc7b46a7fd5304296b5e800e3) More doc cleanup. (@mikeal)
- [f8eb2e2](https://github.com/mikeal/request/commit/f8eb2e229aca38547236d48066a0b3f9f8f67638) Copy headers so that they survive mutation. (@mikeal)
- [59eab0e](https://github.com/mikeal/request/commit/59eab0e5e49c6d32697822f712ed725843e70010) Rolling version. (@mikeal)
- [76bf5f6](https://github.com/mikeal/request/commit/76bf5f6c6e37f6cb972b3d4f1ac495a4ceaaa00d) Improvements to json handling and defaults. (@mikeal)
- [81e2c40](https://github.com/mikeal/request/commit/81e2c4040a9911a242148e1d4a482ac6c745d8eb) Rolling version. (@mikeal)
- [76d8924](https://github.com/mikeal/request/commit/76d8924cab295f80518a71d5903f1e815618414f) Proper checking and handling of json bodies (@mikeal)
- [a8422a8](https://github.com/mikeal/request/commit/a8422a80895ed70e3871c7826a51933a75c51b69) Rolling version. (@mikeal)
- [f236376](https://github.com/mikeal/request/commit/f2363760782c3d532900a86d383c34f3c94f6d5f) Adding pipefilter. (@mikeal)
- [dd85f8d](https://github.com/mikeal/request/commit/dd85f8da969c2cc1825a7dfec6eac430de36440c) Rolling version. (@mikeal)
- [#66](https://github.com/mikeal/request/pull/66) Do not overwrite established content-type headers for read stream deliver (@voodootikigod)
- [b09212f](https://github.com/mikeal/request/commit/b09212f38fe736c2c92a1ee076cae9d0f4c612c3) Do not overwrite established content-type headers for read stream deliveries. (@voodootikigod)
- [01bc25d](https://github.com/mikeal/request/commit/01bc25d25343d73e9f5731b3d0df1cf5923398d4) Only apply workaround on pre-0.5 node.js and move test to assert.equal (@mikeal)
- [d487131](https://github.com/mikeal/request/commit/d487131ebc2f7a4bf265061845f7f3ea2fd3ed34) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [1200df5](https://github.com/mikeal/request/commit/1200df52bd334f9a44a43846159146b8f938fd9e) Rolling version. (@mikeal)
- [8279362](https://github.com/mikeal/request/commit/82793626f6965884a3720d66f5a276d7d4d30873) fix global var leaks (@aheckmann)
- [#67](https://github.com/mikeal/request/pull/67) fixed global variable leaks (@aheckmann)
- [ab91204](https://github.com/mikeal/request/commit/ab9120495a89536c7152e3cdf17d684323b40474) Test that chunked responses are properly toString'ed (@isaacs)
- [9bff39f](https://github.com/mikeal/request/commit/9bff39fa485f28d7f1754e72f026418ca1186783) Properly flatten chunked responses (@isaacs)
- [8e4e956](https://github.com/mikeal/request/commit/8e4e95654391c71c22933ffd422fdc82d20ac059) Fix #52 Make the tests runnable with npm (@isaacs)
- [a9aa9d6](https://github.com/mikeal/request/commit/a9aa9d6d50ef0481553da3e50e40e723a58de10a) Fix #71 Respect the strictSSL flag (@isaacs)
- [#69](https://github.com/mikeal/request/pull/69) Flatten chunked requests properly (@isaacs)
- [#73](https://github.com/mikeal/request/pull/73) Fix #71 Respect the strictSSL flag (@isaacs)
- [#70](https://github.com/mikeal/request/pull/70) add test script to package.json (@isaacs)
- [08ca561](https://github.com/mikeal/request/commit/08ca5617e0d8bcadee98f10f94a49cbf2dd02862) Fixing case where encoding is set. Also cleaning up trailing whitespace because my editor likes to do that now. (@mikeal)
- [0be269f](https://github.com/mikeal/request/commit/0be269f7d9da6c3a14a59d5579546fee9d038960) Fixing case where no body exists. (@mikeal)
- [2f37bbc](https://github.com/mikeal/request/commit/2f37bbc51ff84c3c28ae419138a19bd33a9f0103) Fixing timeout tests. (@mikeal)
- [f551a2f](https://github.com/mikeal/request/commit/f551a2f02a87994249c2fd37dc8f20a29e8bf529) Fixing legacy naming of self as options. (@mikeal)
- [717789e](https://github.com/mikeal/request/commit/717789ec9f690e9d5216ce1c27688eef822940cc) Avoid duplicate emit when using a timeout (@Marsup)
- [#76](https://github.com/mikeal/request/pull/76) Bug when a request fails and a timeout is set (@Marsup)
- [c1d255e](https://github.com/mikeal/request/commit/c1d255e5bcc5791ab69809913fe6d917ab93c8b7) global leakage in request.defaults (@isaacs)
- [14070f2](https://github.com/mikeal/request/commit/14070f269c79cae6ef9e7f7a415867150599bb8e) Don't require SSL for non-SSL requests (@isaacs)
- [4b8f696](https://github.com/mikeal/request/commit/4b8f6965e14c6fb704cf16f5bc011e4787cf32b2) Set proxy auth instead of just setting auth a second time (@isaacs)
- [cd22fbd](https://github.com/mikeal/request/commit/cd22fbdb00b90c5c75187ecf41373cfbb4af5bcd) Merge branch 'proxy-auth-bug' (@isaacs)
- [#78](https://github.com/mikeal/request/pull/78) Don't try to do strictSSL for non-ssl connections (@isaacs)
- [d8c53fc](https://github.com/mikeal/request/commit/d8c53fceca3af385753880395c680f6ec3d4d560) Removing legacy call to sys.puts (@mikeal)
- [731b32b](https://github.com/mikeal/request/commit/731b32b654bb217de3466b8d149ce480988bb24b) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [9c897df](https://github.com/mikeal/request/commit/9c897dffc7e238f10eb7e14c61978d6821c70f56) Enhance redirect handling: (1) response._redirectsFollowed reports the total number of redirects followed instead of being reset to 0; (2) add response.redirects, an array of the response.statusCode and response.headers.location for each redirect. (@danmactough)
- [#81](https://github.com/mikeal/request/pull/81) Enhance redirect handling (@danmactough)
- [4c84001](https://github.com/mikeal/request/commit/4c8400103ec18a0729e29e9ffb17dda65ce02f6d) Document strictSSL option (@isaacs)
- [d517ac0](https://github.com/mikeal/request/commit/d517ac03278b3ebd9a46ca9f263bea68d655822b) allow passing in buffers as multipart bodies (@kkaefer)
- [6563865](https://github.com/mikeal/request/commit/6563865b80573ad3c68834a6633aff6d322b59d5) bugs[web] should be bugs[url] (@isaacs)
- [2625854](https://github.com/mikeal/request/commit/262585480c148c56772dfc8386cfc59d5d262ca0) add option followAllRedirects to follow post/put redirects
- [bc057af](https://github.com/mikeal/request/commit/bc057affb58272d9152766956e5cde4ea51ca043) fix typo, force redirects to always use GET
- [d68b434](https://github.com/mikeal/request/commit/d68b434693dbf848dff4c570c4249a35329cc24f) Support node 0.5.11-style url parsing (@isaacs)
- [#96](https://github.com/mikeal/request/pull/96) Authless parsed url host support (@isaacs)
- [9f66c6d](https://github.com/mikeal/request/commit/9f66c6d79bc6515d870b906df39bd9d6d9164994) Typo, causing 'TypeError: Cannot read property 'length' of undefined' (@isaacs)
- [#97](https://github.com/mikeal/request/pull/97) Typo in previous pull causes TypeError in non-0.5.11 versions (@isaacs)
- [b320e05](https://github.com/mikeal/request/commit/b320e05f2d84510f47a6b6857d091c8cd4d3ae2e) When no request body is being sent set 'content-length':0. fixes #89 (@mikeal)
- [059916c](https://github.com/mikeal/request/commit/059916c545a0faa953cb8ac66b8c3ae243b1c8ce) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [248e9d6](https://github.com/mikeal/request/commit/248e9d65e73ac868948a82d07feaf33387723a1d) Fix for pipe() after response. Added response event, fixed and updated tests, removed deprecated stream objects. (@mikeal)
- [a2e5d6e](https://github.com/mikeal/request/commit/a2e5d6e30d3e101f8c5a034ef0401fdde8608ccf) Fixing double callback firing. node 0.5 is much better about calling errors on the client object which, when aborting on timeout, predictable emits an error which then triggers a double callback. (@mikeal)
- [5f80577](https://github.com/mikeal/request/commit/5f805775e6aeaaf229cc781439b29108fb69f373) Release for 0.6 (@mikeal)
- [bf906de](https://github.com/mikeal/request/commit/bf906de601121b52c433b0af208550f1db892cde) Adding some oauth support, tested with Twitter. (@mikeal)
- [8869b2e](https://github.com/mikeal/request/commit/8869b2e88cc305e224556c5ca75b7b59311911d9) Removing irrelevant comments. (@mikeal)
- [8323eed](https://github.com/mikeal/request/commit/8323eed4915bb73b33544bc276f3840c13969134) Closed issue 82 : handling cookies - added tests too
- [739f841](https://github.com/mikeal/request/commit/739f84166d619778ab96fd0b0f4f1f43e8b0fdda) Closed issue 82 : handling cookies - added tests too
- [7daf841](https://github.com/mikeal/request/commit/7daf8415fb1a4e707ec54eb413169e49d8bbe521) Closed issue 82 : handling cookies - added tests too
- [6c22041](https://github.com/mikeal/request/commit/6c22041a4719bf081c827dda8f35e7b79b4c39d9) changed README
- [3db7f7d](https://github.com/mikeal/request/commit/3db7f7d38e95406b84f06fed52b69038b0250904) Updated README
- [6181b7a](https://github.com/mikeal/request/commit/6181b7a8a4be75bcf75cd3ff6dacb8e910737e92) Documented request.cookie() and request.jar()
- [fc44260](https://github.com/mikeal/request/commit/fc44260d13f0094bfe96d18878a11c6fe88b69e5) Tiny cookie example error on README
- [366831b](https://github.com/mikeal/request/commit/366831b705b5d5ebfbec5f63b4b140cbafcb4515) Remove instanceof check for CookieJar (mikeal suggestion)
- [88488cf](https://github.com/mikeal/request/commit/88488cf076efbd916b0326e0981e280c993963a7) Also add cookie to the user defined cookie jar (mikeal's suggestion)
- [f6fef5b](https://github.com/mikeal/request/commit/f6fef5bfa4ba8e1dfa3022df8991716e5cba7264) Updated cookie documentation in README file
- [b519044](https://github.com/mikeal/request/commit/b5190441a889164dfeb4148fac643fd7a87cfb51) request.defaults({jar: false}) disables cookies && also updated README
- [856a65c](https://github.com/mikeal/request/commit/856a65cd28402efbe3831a68d73937564a27ea9b) Update jar documentation in the options also
- [#102](https://github.com/mikeal/request/pull/102) Implemented cookies - closes issue 82: https://github.com/mikeal/request/issues/82 (@alessioalex)
- [62592e7](https://github.com/mikeal/request/commit/62592e7fe9ee5ecaee80b8f5bc2400e4a277e694) Cookie bugs (@janjongboom)
- [a06ad2f](https://github.com/mikeal/request/commit/a06ad2f955270974409e75c088e1f5d1f5298ff5) Follow redirects should work on PUT and POST requests as well. This is more consistent to other frameworks, e.g. .NET (@janjongboom)
- [bf3f5d3](https://github.com/mikeal/request/commit/bf3f5d30fdabf6946096623fc3398bb66ed19a1f) Cookies shouldn't be discarded when followRedirect = true (@janjongboom)
- [16db85c](https://github.com/mikeal/request/commit/16db85c07e6c2516269299640fdddca6db7bc051) Revert "Follow redirects should work on PUT and POST requests as well. This is more consistent to other frameworks, e.g. .NET" (@janjongboom)
- [841664e](https://github.com/mikeal/request/commit/841664e309f329be98c1a011c634f5291af1eebc) Add test for proxy option (@dominictarr)
- [#105](https://github.com/mikeal/request/pull/105) added test for proxy option. (@dominictarr)
- [50d2d39](https://github.com/mikeal/request/commit/50d2d3934cd86d7142a4aab66017bb1ef82329cf) Fixing test, emitter matches on req.url so it needs the full url. (@mikeal)
- [668a291](https://github.com/mikeal/request/commit/668a291013380af305eba12b1d5c7a5376a74c76) Adding some documentation for OAuth signing support. (@mikeal)
- [04faa3b](https://github.com/mikeal/request/commit/04faa3bf2b1f4ec710414c6ec7231b24767b2f89) Minor improvements in example (@mikeal)
- [0fddc17](https://github.com/mikeal/request/commit/0fddc1798dcd9b213e3f8aec504c61cecf4d7997) Another small fix to the url in the docs. (@mikeal)
- [337649a](https://github.com/mikeal/request/commit/337649a08b4263c0d108cd4621475c8ff9cf8dd0) Add oauth to options. (@mikeal)
- [#86](https://github.com/mikeal/request/pull/86) Can't post binary to multipart requests (@developmentseed)
- [4e4d428](https://github.com/mikeal/request/commit/4e4d4285490be20abf89ff1fb54fb5088c01c00e) Update to Iris Couch URL (@jhs)
- [#110](https://github.com/mikeal/request/pull/110) Update to Iris Couch URL (@iriscouch)
- [d7af099](https://github.com/mikeal/request/commit/d7af0994b382466367f2cafc5376150e661eeb9d) Remove the global `i` as it's causing my test suites to fail with leak detection turned on. (@3rd-Eden)
- [#117](https://github.com/mikeal/request/pull/117) Remove the global `i` (@3rd-Eden)
- [b2a4ad1](https://github.com/mikeal/request/commit/b2a4ad1e7d7553230e932ea093d7f77f38147ef9) Force all cookie keys into lower case as suggested by LinusU (@jhurliman)
- [055a726](https://github.com/mikeal/request/commit/055a7268b40425643d23bd6a4f09c7268dbab680) Applying a modified version of pull request #106 as suggested by janjongboom (@jhurliman)
- [#121](https://github.com/mikeal/request/pull/121) Another patch for cookie handling regression (@jhurliman)
- [a353f4e](https://github.com/mikeal/request/commit/a353f4eeb312ea378d34b624f5c4df33eefa152c) Merge remote-tracking branch 'upstream/master' (@janjongboom)
- [#104](https://github.com/mikeal/request/pull/104) Cookie handling contains bugs (@janjongboom)
- [a3be5ad](https://github.com/mikeal/request/commit/a3be5ad5ea112422ed00da632530b93bcf54727c) Fix encoding of characters like ( (@mikeal)
- [dd2067b](https://github.com/mikeal/request/commit/dd2067bbbf77d1132c9ed480848645136b8a5521) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [ddc4e45](https://github.com/mikeal/request/commit/ddc4e453c3b9a0e11da4df156c5e15206abfc1ef) Pushed new version to npm (@mikeal)
- [feee5eb](https://github.com/mikeal/request/commit/feee5ebd2ca8c09db25b5cb13cd951f7c4322a49) Real fix for encoding issues in javascript and oauth. (@mikeal)
- [23896cd](https://github.com/mikeal/request/commit/23896cdc66d75ec176876167ff21da72b7ff181b) Pushed new version to npm. (@mikeal)
- [a471ed2](https://github.com/mikeal/request/commit/a471ed2ca8acdca1010a0fc20434c5c9956b0d0c) HTTP redirect tests (@jhs)
- [a4a9aa1](https://github.com/mikeal/request/commit/a4a9aa199ff958630791e131092ec332ada00a49) A self-signed certificate for upcoming HTTPS testing (@jhs)
- [10ac6b9](https://github.com/mikeal/request/commit/10ac6b9db40263bec1bf63ee7e057000ffd2d7e9) HTTPS tests, for now a copy of the test-body tests (@jhs)
- [105aed1](https://github.com/mikeal/request/commit/105aed1ff99add1957f91df7efabf406e262f463) Support an "httpModules" object for custom http/https module behavior (@jhs)
- [#112](https://github.com/mikeal/request/pull/112) Support using a custom http-like module (@iriscouch)
- [d05a875](https://github.com/mikeal/request/commit/d05a8753af576fc1adccc7ffe9633690371c05ee) Test for #129 (@mikeal)
- [06cdfaa](https://github.com/mikeal/request/commit/06cdfaa3c29233dac3f47e156f2b5b3a0f0ae4b8) return body as buffer when encoding is null
- [#132](https://github.com/mikeal/request/pull/132) return the body as a Buffer when encoding is set to null (@jahewson)
- [4882e51](https://github.com/mikeal/request/commit/4882e519ed6b8d08795da5de37166148ce0ee440) fixed cookies parsing, updated tests (@afanasy)
- [2be228e](https://github.com/mikeal/request/commit/2be228ec8b48a60028bd1d80c8cbebf23964f913) Change `host` to `hostname` in request hash
- [#135](https://github.com/mikeal/request/pull/135) host vs hostname (@iangreenleaf)
- [e24abc5](https://github.com/mikeal/request/commit/e24abc5cc2c6fa154ae04fe58a16d135eeba4951) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [c99c809](https://github.com/mikeal/request/commit/c99c809bb48b9c0193aae3789c5c844f7f6cbe92) Reverting host -> hostname because it breaks in pre-0.6. (@mikeal)
- [a1134d8](https://github.com/mikeal/request/commit/a1134d855f928fde5c4fe9ee255c111da0195bfc) adding logging (@mikeal)
- [#133](https://github.com/mikeal/request/pull/133) Fixed cookies parsing (@afanasy)
- [9179471](https://github.com/mikeal/request/commit/9179471f9f63b6ba9c9078a35cb888337ce295e8) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [cbb180b](https://github.com/mikeal/request/commit/cbb180b0399074995c235a555e3e3e162d738f7c) Fixes to oauth test. (@mikeal)
- [e1c351f](https://github.com/mikeal/request/commit/e1c351f92958634ccf3fbe78aa2f5b06d9c9a5fa) Published new version. (@mikeal)
- [3ceee86](https://github.com/mikeal/request/commit/3ceee86f1f3aad3a6877d6d3813e087549f3b485) Formatting fixes. (@mikeal)
- [18e1af5](https://github.com/mikeal/request/commit/18e1af5e38168dcb95c8ae29bb234f1ad9bbbdf9) Fixing log error. (@mikeal)
- [edc19b5](https://github.com/mikeal/request/commit/edc19b5249f655714efa0f8fa110cf663b742921) Pushed new version. (@mikeal)
- [f51c32b](https://github.com/mikeal/request/commit/f51c32bd6f4da0419ed8404b610c43ee3f21cf92) added "form" option to readme. (@petejkim)
- [#144](https://github.com/mikeal/request/pull/144) added "form" option to readme (@petejkim)
- [b58022e](https://github.com/mikeal/request/commit/b58022ecda782af93e35e5f9601013b90b09ca73) add "forever" method (@thejh)
- [79d4651](https://github.com/mikeal/request/commit/79d46510ddff2e2c12c69f7ae4072ec489e27b0e) remove logging (@thejh)
- [f87cbf6](https://github.com/mikeal/request/commit/f87cbf6ec6fc0fc2869c340114514c887b304a80) retry on ECONNRESET on reused socket (@thejh)
- [1a91675](https://github.com/mikeal/request/commit/1a916757f4ec48b1282fddfa0aaa0fa6a1bf1267) Multipart requests should respect content-type if set; Issue #145 (@apeace)
- [#146](https://github.com/mikeal/request/pull/146) Multipart should respect content-type if previously set (@apeace)
- [#148](https://github.com/mikeal/request/pull/148) Retry Agent (@thejh)
- [70c5b63](https://github.com/mikeal/request/commit/70c5b63aca29a7d1629fa2909ff5b7199bbf0fd1) Publishing new version to npm. (@mikeal)
- [fc0f04b](https://github.com/mikeal/request/commit/fc0f04bab5d6be56a2c19d47d3e8386bd9a0b29e) Fix: timeout on socket, timeout after redirect
- [ef79e59](https://github.com/mikeal/request/commit/ef79e59bbb88ed3e7d4368fe3ca5eee411bda345) Fix: timeout after redirect 2
- [c32a218](https://github.com/mikeal/request/commit/c32a218da2296e89a269f1832d95b12c4aa10852) merge master (@jroes)
- [d2d9b54](https://github.com/mikeal/request/commit/d2d9b545e5679b829d33deeba0b22f9050fd78b1) add line to docs describing followAllRedirects option (@jroes)
- [#90](https://github.com/mikeal/request/pull/90) add option followAllRedirects to follow post/put redirects (@jroes)
- [c08ab7e](https://github.com/mikeal/request/commit/c08ab7efaefd39c04deb6986716efe5a6069528e) Emit an event after we create the request object so that people can manipulate it before nextTick(). (@mikeal)
- [#162](https://github.com/mikeal/request/pull/162) Fix issue #159 (@dpetukhov)
- [e77a169](https://github.com/mikeal/request/commit/e77a1695c5c632c067857e99274f28a1d74301fe) fixing streaming example. fixes #164 (@mikeal)
- [ee53386](https://github.com/mikeal/request/commit/ee53386d85975c79b801edbb4f5bb7ff4c5dc90b) fixes #127 (@mikeal)
- [e2cd9de](https://github.com/mikeal/request/commit/e2cd9de9a9d10e1aa4cf4e26006bb30fa5086f0b) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [a0ab977](https://github.com/mikeal/request/commit/a0ab9770a8fb89f970bb3783ed4e6dde9e33511b) Added failing test for #125. (@papandreou)
- [c80800a](https://github.com/mikeal/request/commit/c80800a834b0f8bc0fb40d1fad4d4165a83369fd) Fix cookie jar/headers.cookie collision. Closes #125. (@papandreou)
- [1ac9e2d](https://github.com/mikeal/request/commit/1ac9e2d1bf776728a1fe676dd3693ef66f50f7f7) Redirect test: Also assert that the request cookie doesn't get doubled in the request for the landing page. (@papandreou)
- [07bbf33](https://github.com/mikeal/request/commit/07bbf331e2a0d40d261487f6222e8cafee0e50e3) Fixes #150 (@mikeal)
- [c640eed](https://github.com/mikeal/request/commit/c640eed292c06eac3ec89f60031ddf0fc0add732) Cookie jar handling: Don't double the cookies on each redirect (see discussion on #139). (@papandreou)
- [808de8b](https://github.com/mikeal/request/commit/808de8b0ba49d4bb81590ec37a873e6be4d9a416) Adding some missing mime types #138 (@serby)
- [#161](https://github.com/mikeal/request/pull/161) Fix cookie jar/headers.cookie collision (#125) (@papandreou)
- [#168](https://github.com/mikeal/request/pull/168) Picking off an EasyFix by adding some missing mimetypes. (@serby)
- [2a30487](https://github.com/mikeal/request/commit/2a304879f4218c1e46195d882bc81c0f874be329) bugfix - allow add cookie to wrapped request (defaults) (@fabianonunes)
- [a18b4f1](https://github.com/mikeal/request/commit/a18b4f14559f56cf52ca1b421daa6a934d28d51b) Making pipeDest a public prototype method rather than keeping it private. (@mikeal)
- [#170](https://github.com/mikeal/request/pull/170) can't create a cookie in a wrapped request (defaults) (@fabianonunes)
- [49a0f60](https://github.com/mikeal/request/commit/49a0f604779c91dd1759a02cbb195ccbd8d73f5d) Structural refactor, getting read for composable API. (@mikeal)
- [5daa0b2](https://github.com/mikeal/request/commit/5daa0b28b06cf109614f19e76b0e0b9b25ee3baf) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [e4df85c](https://github.com/mikeal/request/commit/e4df85c72221bf09ee7e1eb54f6c881851bd4164) Composable API for OAuth. (@mikeal)
- [945ec40](https://github.com/mikeal/request/commit/945ec40baef968ddd468c3b4dfce01621e4a0e31) Composable form API (@mikeal)
- [c30b47f](https://github.com/mikeal/request/commit/c30b47f229522a75af85da269157377b4a7dc37d) Use this, return this. (@mikeal)
- [e908644](https://github.com/mikeal/request/commit/e908644a69f9107b954f13635736f1e640216aec) Composable multipart API. (@mikeal)
- [e115677](https://github.com/mikeal/request/commit/e115677b1a03576eb96386986c350f211a4f38cd) Composable jar. Guard against overwrites on retry. (@mikeal)
- [a482e48](https://github.com/mikeal/request/commit/a482e4802e11fd122b12e18d1b18b49850fef823) Updating copyright for the new year. (@mikeal)
- [3c6581a](https://github.com/mikeal/request/commit/3c6581a9d4508fe5d75e111ae0fb94c5e0078404) Adding clobber argument for appending to headers. thanks @isaacs (@mikeal)
- [54e6aca](https://github.com/mikeal/request/commit/54e6aca0ab5982621fc9b35500f2154e50c0c95d) Fixes #144. (@mikeal)
- [12f4997](https://github.com/mikeal/request/commit/12f4997ed83bfbfefa3fc5b5635bc9a6829aa0d7) Fixing clobber. (@mikeal)
- [2f34fd1](https://github.com/mikeal/request/commit/2f34fd13b7ec86cb1c67e0a58664b9e060a34a50) Added support for a "query" option value that is a hash of querystring values that is merged (taking precedence over) with the querystring passed in the uri string. (@csainty)
- [a32d9e7](https://github.com/mikeal/request/commit/a32d9e7069533fb727a71730dbaa0f62ebefb731) Added a js based test runner so I can run tests on windows. (@csainty)
- [e0b6ce0](https://github.com/mikeal/request/commit/e0b6ce063de0c4223c97982128bb8203caf4a331) Tidied up an issue where ?> was being appended to URLs. (@csainty)
- [d47150d](https://github.com/mikeal/request/commit/d47150d6748a452df336d8de9743218028a876db) Refactored to match the composable style (@csainty)
- [b7e0929](https://github.com/mikeal/request/commit/b7e0929837873a8132476bb2b4d2e2a0fdc7cd0f) implemented issue #173 allow uri to be first argument (@twilson63)
- [b7264a6](https://github.com/mikeal/request/commit/b7264a6626481d5da50a28c91ea0be7b688c9daf) removed debug line and reset ports (@twilson63)
- [76598c9](https://github.com/mikeal/request/commit/76598c92bee64376e5d431285ac1bf6783140dbb) removed npm-debug (@twilson63)
- [#177](https://github.com/mikeal/request/pull/177) Issue #173 Support uri as first and optional config as second argument (@twilson63)
- [0f24051](https://github.com/mikeal/request/commit/0f240517dea65337636a49cb1cc2b5327504430e) Renamed query to qs. It was actually my first choice, but there appeared to be conflicts with the qs = require('querystring'). These are no longer present though and must have been unrelated. (@csainty)
- [becedaa](https://github.com/mikeal/request/commit/becedaaa7681b0c4ad5c0a9b9922fc950f091af2) Changed test structure to no longer require a server, modeled on the oauth tests. This also lets me revert some of the changes I had to make to the test server and proxy tests (@csainty)
- [9b2bbf0](https://github.com/mikeal/request/commit/9b2bbf0c12e87a59320efac67759041cd4af913f) Modified how the qs function works, it now no longer tweaks the existing request uri, instead it recreates a new one. This allows me to revert all the other changes I had to make previously and gives a nice clean commit that is self contained. (@csainty)
- [5ac7e26](https://github.com/mikeal/request/commit/5ac7e26ce4f7bf5a334df91df83699891171c0ae) failing test for .pipe(dst, opts) (@substack)
- [3b2422e](https://github.com/mikeal/request/commit/3b2422e62fbd6359b841e59a2c1888db71a22c2c) fix for failing pipe opts test (@substack)
- [8788c8b](https://github.com/mikeal/request/commit/8788c8b8cba96662e9d94a96eb04d96b904adea3) added uri param for post, put, head, del shortcuts (@twilson63)
- [#179](https://github.com/mikeal/request/pull/179) fix to add opts in .pipe(stream, opts) (@substack)
- [#180](https://github.com/mikeal/request/pull/180) Modified the post, put, head and del shortcuts to support uri optional param (@twilson63)
- [37d0699](https://github.com/mikeal/request/commit/37d0699eb681e85b7df4896b0a68b6865e596cb3) Fixing end bug i introduced being stupid. (@mikeal)
- [3a97292](https://github.com/mikeal/request/commit/3a97292f45273fa2cc937c0698ba19964780b4bb) fixed defaults functionality to support (uri, options, callback) (@twilson63)
- [#182](https://github.com/mikeal/request/pull/182) Fix request.defaults to support (uri, options, callback) api (@twilson63)
- [c94b200](https://github.com/mikeal/request/commit/c94b200258fa48697e386121a3e114ab7bed2ecf) Switched npm test from the bash script to a node script so that it is cross-platform. (@csainty)
- [#176](https://github.com/mikeal/request/pull/176) Querystring option (@csainty)
- [3b1e609](https://github.com/mikeal/request/commit/3b1e6094451e8d34c93353177de9d76e9a805e43) Adding defaults test back in. (@mikeal)
- [b4ae0c2](https://github.com/mikeal/request/commit/b4ae0c2d50f018a90a3ec8daa1d14c92a99873b9) Fixing idiotic bug I introduced. (@mikeal)
- [32f76c8](https://github.com/mikeal/request/commit/32f76c8baaf784dc2f4f1871153b1796bcebdcfe) Pushed new version to npm. (@mikeal)
- [00d0d9f](https://github.com/mikeal/request/commit/00d0d9f432182f13a5b8aa2e3a2a144b5c179015) Adding accept header to json support. (@mikeal)
- [0f580e6](https://github.com/mikeal/request/commit/0f580e6f6317c5301a52c0b6963d58e27112abca) Add abort support to the returned request (@itay)
- [4505e6d](https://github.com/mikeal/request/commit/4505e6d39a44229bfe5dc4d9a920233e05a7dfdb) Fixing some edge streaming cases with redirects by reusing the Request object. (@mikeal)
- [eed57af](https://github.com/mikeal/request/commit/eed57af8fe3e16632e9e0043d4d7f4d147dbfb8f) Published new version. (@mikeal)
- [97386b5](https://github.com/mikeal/request/commit/97386b5d7315b5c83702ffc7d0b09e34ecb67e04) Fixing pretty bad bug from the composable refactor. (@mikeal)
- [b693ce6](https://github.com/mikeal/request/commit/b693ce64e16aaa859d4edc86f82fbb11e00d33c0) Move abort to a prototype method, don't raise error (@itay)
- [1330eef](https://github.com/mikeal/request/commit/1330eef3ec84a651a435c95cf1ff1a4003086440) Merge branch 'master' of git://github.com/mikeal/request (@itay)
- [#188](https://github.com/mikeal/request/pull/188) Add abort support to the returned request (@itay)
- [5ff4645](https://github.com/mikeal/request/commit/5ff46453e713da1ae66a0d510eda4919e4080abe) Style changes. (@mikeal)
- [2dbd1e4](https://github.com/mikeal/request/commit/2dbd1e4350c2941b795b0e5ee7c0a00cd04cce09) Fixing new params style on master for head request. (@mikeal)
- [14989b2](https://github.com/mikeal/request/commit/14989b2dfc6830dbdad5364930fba1d2995aba06) Pushed new version to npm. (@mikeal)
- [0ea2351](https://github.com/mikeal/request/commit/0ea2351ef017ada9b8472f8d73086715ebe30c6a) Fixes #190. outdated check on options.json from before we had boolean support. (@mikeal)
- [21bf78c](https://github.com/mikeal/request/commit/21bf78c264316f75f4e6c571461521cda6ccf088) Adds a block on DELETE requests in status 300-400 (@goatslacker)
- [0c0c201](https://github.com/mikeal/request/commit/0c0c20139b28b21a860f72b8ce0124046fae421d) Adds tests for GH-119 Fix (@goatslacker)
- [#193](https://github.com/mikeal/request/pull/193) Fixes GH-119 (@goatslacker)
- [5815a69](https://github.com/mikeal/request/commit/5815a697347f20658dc2bdfd0d06e41d0aa0dac4) Fixes #194. setTimeout only works on node 0.6+ (@mikeal)
- [1ddcd60](https://github.com/mikeal/request/commit/1ddcd605bc8936c5b3534e1cf9aa1b29fa2b060b) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [7b35b4f](https://github.com/mikeal/request/commit/7b35b4ff63bbdf133f0f600a88a87b5723d29bdf) Removing old checks for self.req, it's ensured if start() is called. Implementing early pause/resume for when streams try to pause/resume before any data is emitted. Fixes #195. (@mikeal)
- [f01b79b](https://github.com/mikeal/request/commit/f01b79bb651f64065bac8877739223527f5b5592) Make ForeverAgent work with HTTPS (@isaacs)
- [#197](https://github.com/mikeal/request/pull/197) Make ForeverAgent work with HTTPS (@isaacs)
- [8d85b57](https://github.com/mikeal/request/commit/8d85b57ebb81c9d2d0a6b94aed41bf2ab0e3ad09) Forever inherits bugfix (@isaacs)
- [#198](https://github.com/mikeal/request/pull/198) Bugfix on forever usage of util.inherits (@isaacs)
- [37446f5](https://github.com/mikeal/request/commit/37446f54bb21cf9c83ffa81d354d799ae7ecf9ed) Add a test of HTTPS strict with CA checking (@isaacs)
- [8378d2e](https://github.com/mikeal/request/commit/8378d2ef9b8121a9851d21b3f6ec8304bde61c9d) Support tunneling HTTPS requests over proxies (@isaacs)
- [#199](https://github.com/mikeal/request/pull/199) Tunnel (@isaacs)
- [f0052ac](https://github.com/mikeal/request/commit/f0052ac5e6ca9f3f4aa49f6cda6ba15eb5d8b8e6) Published new version to npm. (@mikeal)
- [cea668f](https://github.com/mikeal/request/commit/cea668f6f7d444831313ccc0e0d301d25f2bd421) Adding more explicit error when undefined is passed as uri or options. (@mikeal)
- [047b7b5](https://github.com/mikeal/request/commit/047b7b52f3b11f4c44a02aeb1c3583940ddb59c7) Fix special method functions that get passed an options object. (@mikeal)
- [746de0e](https://github.com/mikeal/request/commit/746de0ef2f564534b29eeb8f296a59bd2c3086a7) pass through Basic authorization option for HTTPS tunneling
- [6fda9d7](https://github.com/mikeal/request/commit/6fda9d7d75e24cc1302995e41e26a91e03fdfc9a) Always clobber internal objects for qs but preserve old querystring args when clobber is present. (@mikeal)
- [75ca7a2](https://github.com/mikeal/request/commit/75ca7a25bc9c6102e87f3660a25835c7fcd70edb) Merge branch 'master' of https://github.com/mikeal/request
- [3b9f0fd](https://github.com/mikeal/request/commit/3b9f0fd3da4ae74de9ec76e7c66c57a7f8641df2) Fix cookies so that attributes are case insensitive
- [fddbd6e](https://github.com/mikeal/request/commit/fddbd6ee7d531bc4a82f629633b9d1637cb039e8) Properly set cookies during redirects
- [0d0bdb7](https://github.com/mikeal/request/commit/0d0bdb793f908492d4086fae8744f1e33e68d8c6) Remove request body when following non-GET redirects
- [#203](https://github.com/mikeal/request/pull/203) Fix cookie and redirect bugs and add auth support for HTTPS tunnel (@milewise)
- [b5fa773](https://github.com/mikeal/request/commit/b5fa773994de1799cf53491db7f5f3ba32825b20) Replace all occurrences of special chars in RFC3986 (@chriso)
- [bc6cd6c](https://github.com/mikeal/request/commit/bc6cd6ca6c6157bad76f0b2b23d4993f389ba977) documenting additional behavior of json option (@jphaas)
- [80e4e43](https://github.com/mikeal/request/commit/80e4e43186de1e9dcfaa1c9a921451560b91267c) Fixes #215. (@mikeal)
- [51f343b](https://github.com/mikeal/request/commit/51f343b9adfc11ec1b2ddcfb52a57e1e13feacb2) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [89c0f1d](https://github.com/mikeal/request/commit/89c0f1dd324bc65ad9c07436fb2c8220de388c42) titlecase authorization for oauth (@visnup)
- [#217](https://github.com/mikeal/request/pull/217) need to use Authorization (titlecase) header with Tumblr OAuth (@visnup)
- [8c163eb](https://github.com/mikeal/request/commit/8c163eb9349459839fc720658979d5c97a955825) Double quotes are optional, and the space after the ; could be required (@janjongboom)
- [#224](https://github.com/mikeal/request/pull/224) Multipart content-type change (@janjongboom)
- [96f4b9b](https://github.com/mikeal/request/commit/96f4b9b1f7b937a92f3f94f10d6d02f8878b6107) Style changes. (@mikeal)
- [b131c64](https://github.com/mikeal/request/commit/b131c64816f621cf15f8c51e76eb105778b4aad8) Adding safe .toJSON method. fixes #167 (@mikeal)
- [05d6e02](https://github.com/mikeal/request/commit/05d6e02c31ec4e6fcfadbfbe5414e701710f6e55) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [74ca9a4](https://github.com/mikeal/request/commit/74ca9a4852b666d30dd71421e8cc8b8a83177148) Unified error and complete handling. Fixes #171 (@mikeal)
- [a86c7dc](https://github.com/mikeal/request/commit/a86c7dc7d0a7c640c7def4c0215e46e76a11ff56) Fixing followAllRedirects and all the redirect tests. (@mikeal)
- [#211](https://github.com/mikeal/request/pull/211) Replace all occurrences of special chars in RFC3986 (@chriso)
- [7e24e8a](https://github.com/mikeal/request/commit/7e24e8a48d0dcfe10d0cc08b3c4e9627b9a95a97) New version on npm, first 3.0 release candidate. (@mikeal)
- [22e0f0d](https://github.com/mikeal/request/commit/22e0f0d73459c11b81b0f66a2cde85492dd8e38f) Added test for .toJSON() (@mikeal)
- [df32746](https://github.com/mikeal/request/commit/df32746f157948b6ae05e87a35cf1768e065ef0b) Adding toJSON to npm test. (@mikeal)
- [e65bfba](https://github.com/mikeal/request/commit/e65bfba98f0886a059a268dcdceabf41aec1e5cc) New version in npm. (@mikeal)
- [2b95921](https://github.com/mikeal/request/commit/2b959217151aaff7a6e7cc15e2acfccd1bbb9b85) Fixing defaults when url is passed instead of uri. (@mikeal)
- [e0534d8](https://github.com/mikeal/request/commit/e0534d860b4931a7a6e645b328fd4418a5433057) Pushed new version to npm. (@mikeal)
- [d2dc835](https://github.com/mikeal/request/commit/d2dc83538379e9e1fafb94f5698c56b4a5318d8d) don't error when null is passed for options (@polotek)
- [db80bf0](https://github.com/mikeal/request/commit/db80bf0444bd98c45f635f305154b9da20eed328) expose initParams (@polotek)
- [8cf019c](https://github.com/mikeal/request/commit/8cf019c9f9f719694408840823e92da08ab9dac3) allow request.defaults to override the main request method (@polotek)
- [#240](https://github.com/mikeal/request/pull/240) don't error when null is passed for options (@polotek)
- [69d017d](https://github.com/mikeal/request/commit/69d017de57622429f123235cc5855f36b3e18d1c) added dynamic boundary for multipart requests (@zephrax)
- [fc13e18](https://github.com/mikeal/request/commit/fc13e185f5e28a280d347e61622ba708e1cd7bbc) added dynamic boundary for multipart requests (@zephrax)
- [#243](https://github.com/mikeal/request/pull/243) Dynamic boundary (@zephrax)
- [1764176](https://github.com/mikeal/request/commit/176417698a84c53c0a69bdfd2a05a2942919816c) Fixing the set-cookie header (@jeromegn)
- [#246](https://github.com/mikeal/request/pull/246) Fixing the set-cookie header (@jeromegn)
- [6f9da89](https://github.com/mikeal/request/commit/6f9da89348b848479c23192c04b3c0ddd5a4c8bc) do not set content-length header to 0 when self.method is GET or self.method is undefined (@sethbridges)
- [efc0ea4](https://github.com/mikeal/request/commit/efc0ea44d63372a30011822ad9d37bd3d7b85952) Experimental AWS signing. Signing code from knox. (@mikeal)
- [4c08a1c](https://github.com/mikeal/request/commit/4c08a1c10bc0ebb679e212ad87419f6c4cc341eb) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [fdb10eb](https://github.com/mikeal/request/commit/fdb10eb493110b8e6e4f679524f38cef946e3f08) Adding support for aws in options. (@mikeal)
- [dac6a30](https://github.com/mikeal/request/commit/dac6a301ae03207af88fae6f5017e82157b79b41) Fixing upgraded stat size and supporting content-type and content-md5 properly. (@mikeal)
- [98cb503](https://github.com/mikeal/request/commit/98cb50325e1d7789fd9f44523d2315df5f890d10) Allow body === '' /* the empty string */. (@Filirom1)
- [0e9ac12](https://github.com/mikeal/request/commit/0e9ac12c69aaca370fbca94b41358e1c3a2f6170) fixed just another global leak of i (@sreuter)
- [#260](https://github.com/mikeal/request/pull/260) fixed just another leak of 'i' (@sreuter)
- [#255](https://github.com/mikeal/request/pull/255) multipart allow body === '' ( the empty string ) (@Filirom1)
- [#249](https://github.com/mikeal/request/pull/249) Fix for the fix of your (closed) issue #89 where self.headers[content-length] is set to 0 for all methods (@sethbridges)
- [adc9ab1](https://github.com/mikeal/request/commit/adc9ab1f563f3cb4681ac8241fcc75e6099efde2) style changes. making @rwaldron cry (@mikeal)
- [155e6ee](https://github.com/mikeal/request/commit/155e6ee270924d5698d3fea37cefc1926cbaf998) Fixed `pool: false` to not use the global agent (@timshadel)
- [1232a8e](https://github.com/mikeal/request/commit/****************************************) JSON test should check for equality (@timshadel)
- [#261](https://github.com/mikeal/request/pull/261) Setting 'pool' to 'false' does NOT disable Agent pooling (@timshadel)
- [#262](https://github.com/mikeal/request/pull/262) JSON test should check for equality (@timshadel)
- [914a723](https://github.com/mikeal/request/commit/914a72300702a78a08263fe98a43d25e25713a70) consumer_key and token_secret need to be encoded for OAuth v1 (@nanodocumet)
- [500e790](https://github.com/mikeal/request/commit/500e790f8773f245ff43dd9c14ec3d5c92fe0b9e) Fix uncontrolled crash when "this.uri" is an invalid URI (@naholyr)
- [#265](https://github.com/mikeal/request/pull/265) uncaughtException when redirected to invalid URI (@naholyr)
- [#263](https://github.com/mikeal/request/pull/263) Bug in OAuth key generation for sha1 (@nanodocumet)
- [f4b87cf](https://github.com/mikeal/request/commit/f4b87cf439453b3ca1d63e85b3aeb3373ee1f17e) I'm not OCD seriously (@TehShrike)
- [#268](https://github.com/mikeal/request/pull/268) I'm not OCD seriously (@TehShrike)
- [fcab7f1](https://github.com/mikeal/request/commit/fcab7f1953cd6fb141a7d98f60580c50b59fb73f) Adding a line break to the preamble as the first part of a multipart was not recognized by a server I was communicating with. (@proksoup)
- [661b62e](https://github.com/mikeal/request/commit/661b62e5319bf0143312404f1fc81c895c46f6e6) Commenting out failing post test. Need to figure out a way to test this now that the default is to use a UUID for the frontier. (@mikeal)
- [7165c86](https://github.com/mikeal/request/commit/7165c867fa5dea4dcb0aab74d2bf8ab5541e3f1b) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [5a7ca9b](https://github.com/mikeal/request/commit/5a7ca9b398c1300c08a28fb7f266054c3ce8c57a) Added drain event and returning the boolean from write to proper handle back pressure when piping. (@mafintosh)
- [#273](https://github.com/mikeal/request/pull/273) Pipe back pressure issue (@mafintosh)
- [f8ae8d1](https://github.com/mikeal/request/commit/f8ae8d18627e4743996d8600f77f4e4c05a2a590) New version in npm. (@mikeal)
- [7ff5dae](https://github.com/mikeal/request/commit/7ff5daef152bcfac5b02e661e5476a57b9693489) Merge remote-tracking branch 'upstream/master' (@proksoup)
- [1f34700](https://github.com/mikeal/request/commit/1f34700e5614ea2a2d78b80dd467c002c3e91cb3) fix tests with boundary by injecting boundry from header (@benatkin)
- [ee2b2c2](https://github.com/mikeal/request/commit/ee2b2c2f7a8625fde4d71d79e19cdc5d98f09955) Like in [node.js](https://github.com/joyent/node/blob/master/lib/net.js#L52) print logs if NODE_DEBUG contains the word request (@Filirom1)
- [#279](https://github.com/mikeal/request/pull/279) fix tests with boundary by injecting boundry from header (@benatkin)
- [3daebaf](https://github.com/mikeal/request/commit/3daebaf2551c8d0df7dac1ebff0af4fe08608768) Merge branch 'master' of https://github.com/mikeal/request (@proksoup)
- [dba2ebf](https://github.com/mikeal/request/commit/dba2ebf09552258f37b60122c19b236064b0d216) Updating with corresponding tests. (@proksoup)
- [396531d](https://github.com/mikeal/request/commit/396531d083c94bc807a25f7c3a50a0c92a00c5f7) Removing console.log of multipart (@proksoup)
- [54226a3](https://github.com/mikeal/request/commit/54226a38816b4169e0a7a5d8b1a7feba78235fec) Okay, trying it as an optional parameter, with a new test in test-body.js to verify (@proksoup)
- [23ae7d5](https://github.com/mikeal/request/commit/23ae7d576cc63d645eecf057112b71d6cb73e7b1) Remove non-"oauth_" parameters from being added into the OAuth Authorization header (@jplock)
- [8b82ef4](https://github.com/mikeal/request/commit/8b82ef4ff0b50b0c8dcfb830f62466fa30662666) Removing guard, there are some cases where this is valid. (@mikeal)
- [82440f7](https://github.com/mikeal/request/commit/82440f76f22a5fca856735af66e2dc3fcf240c0d) Adding back in guard for _started, need to keep some measure of safety but we should defer this restriction for as long as possible. (@mikeal)
- [#282](https://github.com/mikeal/request/pull/282) OAuth Authorization header contains non-"oauth_" parameters (@jplock)
- [087be3e](https://github.com/mikeal/request/commit/087be3ebbada53699d14839374f1679f63f3138f) Remove stray `console.log()` call in multipart generator. (@bcherry)
- [0a8a5ab](https://github.com/mikeal/request/commit/0a8a5ab6a08eaeffd45ef4e028be2259d61bb0ee) Merge remote-tracking branch 'upstream/master' (@proksoup)
- [#241](https://github.com/mikeal/request/pull/241) Composability updates suggested by issue #239 (@polotek)
- [#284](https://github.com/mikeal/request/pull/284) Remove stray `console.log()` call in multipart generator. (@bcherry)
- [8344666](https://github.com/mikeal/request/commit/8344666f682a302c914cce7ae9cea8de054f9240) Fix #206 Change HTTP/HTTPS agent when redirecting between protocols (@isaacs)
- [#272](https://github.com/mikeal/request/pull/272) Boundary begins with CRLF? (@proksoup)
- [#214](https://github.com/mikeal/request/pull/214) documenting additional behavior of json option (@jphaas)
- [#207](https://github.com/mikeal/request/pull/207) Fix #206 Change HTTP/HTTPS agent when redirecting between protocols (@isaacs)
- [9cadd61](https://github.com/mikeal/request/commit/9cadd61d989e85715ea07da8770a3077db41cca3) Allow parser errors to bubble up to request (@mscdex)
- [6a00fea](https://github.com/mikeal/request/commit/6a00fea09eed99257c0aec2bb66fbf109b0f573a) Only add socket error handler callback once (@mscdex)
- [975ea90](https://github.com/mikeal/request/commit/975ea90bed9503c67055b20e36baf4bcba54a052) Fix style (@mscdex)
- [205dfd2](https://github.com/mikeal/request/commit/205dfd2e21c13407d89d3ed92dc2b44b987d962b) Use .once() when listening for parser error (@mscdex)
- [ff9b564](https://github.com/mikeal/request/commit/ff9b5643d6b5679a9e7d7997ec6275dac10b000e) Add a space after if (@Filirom1)
- [#280](https://github.com/mikeal/request/pull/280) Like in node.js print options if NODE_DEBUG contains the word request (@Filirom1)
- [d38e57b](https://github.com/mikeal/request/commit/d38e57bbb3d827aa87427f2130aa5a5a3a973161) Test for #289 (@isaacs)
- [820af58](https://github.com/mikeal/request/commit/820af5839f2a193d091d98f23fd588bd919e3e58) A test of POST redirect following with 303 status (@isaacs)
- [7adc5a2](https://github.com/mikeal/request/commit/7adc5a21869bc92cc3b5e84d32c585952c8e5e87) Use self.encoding when calling Buffer.toString() (@isaacs)
- [#290](https://github.com/mikeal/request/pull/290) A test for #289 (@isaacs)
- [#293](https://github.com/mikeal/request/pull/293) Allow parser errors to bubble up to request (@mscdex)
- [ed68b8d](https://github.com/mikeal/request/commit/ed68b8dd024561e9d47d80df255fb79d783c13a7) Updated the twitter oauth dance. The comments weren't clear. Also removed token_key. No longer needed with twitter oauth. (@joemccann)
- [6bc19cd](https://github.com/mikeal/request/commit/6bc19cda351b59f8e45405499a100abd0b456e42) Forgot to remove token_secret; no longer needed for twitter. (@joemccann)
- [1f21b17](https://github.com/mikeal/request/commit/1f21b17fc4ff3a7011b23e3c9261d66effa3aa40) Adding form-data support. (@mikeal)
- [827e950](https://github.com/mikeal/request/commit/827e950500746eb9d3a3fa6f174416b194c9dedf) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [b211200](https://github.com/mikeal/request/commit/b2112009a31fc7f9122970d392750f62b6e77111) Test fixes for relative import. Adding to run all (@mikeal)
- [1268195](https://github.com/mikeal/request/commit/1268195b75bd5bb3954b4c4f2d9feb80a97994d1) Bundling mime module rather than keep around our own mime-map. (@mikeal)
- [4f51cec](https://github.com/mikeal/request/commit/4f51cecdc363946b957585c3deccfd8c37e19aa0) Docs for the form API, pumping version. (@mikeal)
- [90245d7](https://github.com/mikeal/request/commit/90245d7199215d7b195cf7e36b203ca0bd0a6bd3) Doc fixes. (@mikeal)
- [d98ef41](https://github.com/mikeal/request/commit/d98ef411c560bd1168f242c524a378914ff8eac4) Pushed new version to npm. (@mikeal)
- [3e11937](https://github.com/mikeal/request/commit/3e119375acda2da225afdb1596f6346dbd551fba) Pass servername to tunneling secure socket creation (@isaacs)
- [7725b23](https://github.com/mikeal/request/commit/7725b235fdec8889c0c91d55c99992dc683e2e22) Declare dependencies more sanely (@isaacs)
- [#317](https://github.com/mikeal/request/pull/317) Workaround for #313 (@isaacs)
- [#318](https://github.com/mikeal/request/pull/318) Pass servername to tunneling secure socket creation (@isaacs)
- [0c470bc](https://github.com/mikeal/request/commit/0c470bccf1ec097ae600b6116e6244cb624dc00e) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [0d98e5b](https://github.com/mikeal/request/commit/0d98e5b7ea6bd9c4f21535d3682bbed2f2e05df4) Pushed new version to npm. (@mikeal)
- [64a4448](https://github.com/mikeal/request/commit/64a44488ac8c792a1f548f305fc5c61efe0d77fb) when setting defaults, the wrapper adds the jar method assuming it has the same signature as get, meaning undefined is passed into initParams, which subsequently fails.  now passing jar function directly as it has no need of defaults anyway seeing as it only creates a new cookie jar (@StuartHarris)
- [48c9881](https://github.com/mikeal/request/commit/48c988118bda4691fffbfcf30d5a39b6c1438736) Added test to illustrate #321 (@alexindigo)
- [8ce0f2a](https://github.com/mikeal/request/commit/8ce0f2a3b6929cd0f7998e00d850eaf5401afdb7) Added *src* stream removal on redirect. #321 (@alexindigo)
- [c32f0bb](https://github.com/mikeal/request/commit/c32f0bb9feaa71917843856c23b4aae99f78ad4d) Do not try to remove listener from an undefined connection (@strk)
- [#326](https://github.com/mikeal/request/pull/326) Do not try to remove listener from an undefined connection (@CartoDB)
- [#322](https://github.com/mikeal/request/pull/322) Fix + test for piped into request bumped into redirect. #321 (@alexindigo)
- [85b6a63](https://github.com/mikeal/request/commit/85b6a632ac7d3456485fbf931043f10f5f6344a5) New version in npm. (@mikeal)
- [f462bd3](https://github.com/mikeal/request/commit/f462bd3fa421fa5e5ca6c91852333db90297b80e) Rolling trunk version. (@mikeal)
- [8a82c5b](https://github.com/mikeal/request/commit/8a82c5b0990cc58fa4cb7f81814d13ba7ae35453) Adding url to redirect error for better debugging. (@mikeal)
- [013c986](https://github.com/mikeal/request/commit/013c986d0a8b5b2811cd06dd3733f4a3d37df1cc) Better debugging of max redirect errors. (@mikeal)
- [#320](https://github.com/mikeal/request/pull/320) request.defaults() doesn't need to wrap jar() (@redbadger)
- [4797f88](https://github.com/mikeal/request/commit/4797f88b42c3cf8680cbde09bf473678a5707aed) Fix #296 - Only set Content-Type if body exists (@Marsup)
- [f6bcf3e](https://github.com/mikeal/request/commit/f6bcf3eb51982180e813c69cccb942734f815ffe) fixup aws function to work in more situations (@nlf)
- [ba6c88a](https://github.com/mikeal/request/commit/ba6c88af5e771c2a0e007e6166e037a149561e09) added short blurb on using aws (@nlf)
- [#343](https://github.com/mikeal/request/pull/343) Allow AWS to work in more situations, added a note in the README on its usage (@nathan-lafreniere)
- [288c52a](https://github.com/mikeal/request/commit/288c52a2a1579164500c26136552827112801ff1) switch to a case insensitive getter when fetching headers for aws auth signing (@nlf)
- [#332](https://github.com/mikeal/request/pull/332) Fix #296 - Only set Content-Type if body exists (@Marsup)
- [7a16286](https://github.com/mikeal/request/commit/7a162868de65b6de15e00c1f707b5e0f292c5f86) Emit errors for anything in init so that it is catchable in a redirect. (@mikeal)
- [d288d21](https://github.com/mikeal/request/commit/d288d21d709fa81067f5af53737dfde06f842262) fix bug (@azylman)
- [#355](https://github.com/mikeal/request/pull/355) stop sending erroneous headers on redirected requests (@azylman)
- [b0b97f5](https://github.com/mikeal/request/commit/b0b97f53a9e94f3aeaa05e2cda5b820668f6e3b2) delete _form along with everything else on a redirect (@jgautier)
- [#360](https://github.com/mikeal/request/pull/360) Delete self._form along with everything else on redirect (@jgautier)
- [61e3850](https://github.com/mikeal/request/commit/61e3850f0f91ca6732fbd06b46796fbcd2fea1ad) Made it so that if we pass in Content-Length or content-length in the headers, don't make a new version (@danjenkins)
- [#361](https://github.com/mikeal/request/pull/361) Don't create a Content-Length header if we already have it set (@danjenkins)
- [590452d](https://github.com/mikeal/request/commit/590452d6569e68e480d4f40b88022f1b81914ad6) inside oauth.hmacsign: running rfc3986 on base_uri instead of just encodeURIComponent.
- [#362](https://github.com/mikeal/request/pull/362) Running `rfc3986` on `base_uri` in `oauth.hmacsign` instead of just `encodeURIComponent` (@jeffmarshall)
- [f7dc90c](https://github.com/mikeal/request/commit/f7dc90c8dae743d5736dc6c807eecde613eb4fd4) Revert "Merge pull request #362 from jeffmarshall/master" (@mikeal)
- [d631a26](https://github.com/mikeal/request/commit/d631a26e263077eca3d4925de9b0a8d57365ba90) reintroducing the WTF escape + encoding, also fixing a typo.
- [#363](https://github.com/mikeal/request/pull/363) rfc3986 on base_uri, now passes tests (@jeffmarshall)
- [bfe2791](https://github.com/mikeal/request/commit/bfe2791f596b749eed6961159d41a404c3aba0d0) oauth fix. (@mikeal)
- [#344](https://github.com/mikeal/request/pull/344) Make AWS auth signing find headers correctly (@nathan-lafreniere)
- [e863f25](https://github.com/mikeal/request/commit/e863f25336abc7b9f9936c20e0c06da8db0c6593) style change. (@mikeal)
- [3e5a87c](https://github.com/mikeal/request/commit/3e5a87ce28b3bb45861b32f283cd20d0084d78a7) Don't remove x_auth_type for Twitter reverse auth (@drudge)
- [#369](https://github.com/mikeal/request/pull/369) Don't remove x_auth_mode for Twitter reverse auth (@drudge)
- [25d4667](https://github.com/mikeal/request/commit/25d466773c43949e2eea4236ffc62841757fd1f0) x_auth_mode not x_auth_type (@drudge)
- [#370](https://github.com/mikeal/request/pull/370) Twitter reverse auth uses x_auth_mode not x_auth_type (@drudge)
- [cadf4dc](https://github.com/mikeal/request/commit/cadf4dc54f4ee3fae821f6beb1ea6443e528bf6f) massive style commit. (@mikeal)
- [33453a5](https://github.com/mikeal/request/commit/33453a53bc37e4499853b9d929b3603cdf7a31cd) New version in npm. (@mikeal)
- [b638185](https://github.com/mikeal/request/commit/b6381854006470af1d0607f636992c7247b6720f) Setting master version. (@mikeal)
- [8014d2a](https://github.com/mikeal/request/commit/8014d2a5b797f07cf56d2f39a346031436e1b064) correct Host header for proxy tunnel CONNECT (@ypocat)
- [#374](https://github.com/mikeal/request/pull/374) Correct Host header for proxy tunnel CONNECT (@ypocat)
- [8c3e9cb](https://github.com/mikeal/request/commit/8c3e9cb529767cff5e7206e2e76531183085b42a) If one of the request parameters is called "timestamp", the "oauth_timestamp" OAuth parameter will get removed during the parameter cleanup loop. (@jplock)
- [#375](https://github.com/mikeal/request/pull/375) Fix for missing oauth_timestamp parameter (@jplock)
- [69e6dc5](https://github.com/mikeal/request/commit/69e6dc5c80e67bbd7d135c3ceb657a1b2df58763) Fixed headers piping on redirects (@kapetan)
- [#376](https://github.com/mikeal/request/pull/376) Headers lost on redirect (@kapetan)
- [62dbbf3](https://github.com/mikeal/request/commit/62dbbf3d77b0851ba424d4f09d1d0c0be91c1f2d) Resolving the Invalid signature when using "qs" (@landeiro)
- [d4cf4f9](https://github.com/mikeal/request/commit/d4cf4f98e11f9a85b6bdfd0481c85c8ac34061ce) fixes missing host header on retried request when using forever agent
- [#380](https://github.com/mikeal/request/pull/380) Fixes missing host header on retried request when using forever agent (@mac-)
- [#381](https://github.com/mikeal/request/pull/381) Resolving "Invalid signature. Expected signature base string: " (@landeiro)
- [ea2f975](https://github.com/mikeal/request/commit/ea2f975ae83efe956b77cbcd0fd9ad42c0d5192f) Ensure that uuid is treated as a property name, not an index. (@othiym23)
- [#388](https://github.com/mikeal/request/pull/388) Ensure "safe" toJSON doesn't break EventEmitters (@othiym23)
- [11a3bc0](https://github.com/mikeal/request/commit/11a3bc0ea3063f6f0071248e03c8595bfa9fd046) Add more reporting to tests (@mmalecki)
- [#398](https://github.com/mikeal/request/pull/398) Add more reporting to tests (@mmalecki)
- [b85bf63](https://github.com/mikeal/request/commit/b85bf633fe8197dc38855f10016a0a76a8ab600a) Optimize environment lookup to happen once only (@mmalecki)
- [#403](https://github.com/mikeal/request/pull/403) Optimize environment lookup to happen once only (@mmalecki)
- [dbb9a20](https://github.com/mikeal/request/commit/dbb9a205fafd7bf5a05d2dbe7eb2c6833b4387dc) renaming tests/googledoodle.png to match it's actual image type of jpeg (@nfriedly)
- [e2d7d4f](https://github.com/mikeal/request/commit/e2d7d4fd35869354ba14a333a4b4989b648e1971) Add more auth options, including digest support (@nylen)
- [d0d536c](https://github.com/mikeal/request/commit/d0d536c1e5a9a342694ffa5f14ef8fbe8dcfa8bd) Add tests for basic and digest auth (@nylen)
- [85fd359](https://github.com/mikeal/request/commit/85fd359890646ef9f55cc6e5c6a32e74f4fbb786) Document new auth options (@nylen)
- [#338](https://github.com/mikeal/request/pull/338) Add more auth options, including digest support (@nylen)
- [fd2e2fa](https://github.com/mikeal/request/commit/fd2e2fa1e6d580cbc34afd3ae1200682cecb3cf9) Fixed a typo. (@jerem)
- [#415](https://github.com/mikeal/request/pull/415) Fixed a typo. (@jerem)
- [53c1508](https://github.com/mikeal/request/commit/53c1508c9c6a58f7d846de82cad36402497a4a4f) Fix for #417 (@mikeal)
- [b23f985](https://github.com/mikeal/request/commit/b23f985e02da4a96f1369541a128c4204a355666) Fixing merge conflict. (@mikeal)
- [28e8be5](https://github.com/mikeal/request/commit/28e8be5175793ac99236df88e26c0139a143e32d) Lost a forever fix in the previous merge. Fixing. (@mikeal)
- [e4d1e25](https://github.com/mikeal/request/commit/e4d1e25c1648ef91f6baf1ef407c712509af4b66) Copy options before adding callback. (@nrn)
- [22bc67d](https://github.com/mikeal/request/commit/22bc67d7ac739e9c9f74c026f875a0a7c686e29d) Respect specified {Host,host} headers, not just {host} (@andrewschaaf)
- [#430](https://github.com/mikeal/request/pull/430) Respect specified {Host,host} headers, not just {host} (@andrewschaaf)
- [6b11acf](https://github.com/mikeal/request/commit/6b11acf3e29fb84daef4e940314cae5ac2e580c6) Updating form-data. (@mikeal)
- [d195845](https://github.com/mikeal/request/commit/d195845c3e1de42c9aee752eec8efa4dda87ec74) Updating mime (@mikeal)
- [20ba1d6](https://github.com/mikeal/request/commit/20ba1d6d38191aa7545b927a7262a18c5c63575b) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [0150d9f](https://github.com/mikeal/request/commit/0150d9fa13e51d99880013b9ec29343850b40c2f) Consider `options.rejectUnauthorized` when pooling https agents (@mmalecki)
- [3e07b6d](https://github.com/mikeal/request/commit/3e07b6d4b81037d0e6e595670db483708ffa8698) Use `rejectUnauthorized: false` in tests (@mmalecki)
- [3995878](https://github.com/mikeal/request/commit/3995878d9fff18a8707f27ffeb4ed6401086adce) Support `key` and `cert` options (@mmalecki)
- [#433](https://github.com/mikeal/request/pull/433) Added support for HTTPS cert & key (@indexzero)
- [8b0f4e8](https://github.com/mikeal/request/commit/8b0f4e8fba33d578a891218201d87e3316ea9844) Released 2.14.0 (@mikeal)
- [54172c6](https://github.com/mikeal/request/commit/54172c68cab8360372e1e64e3fa14902662950bd) Rolling master version. (@mikeal)
- [aa4a285](https://github.com/mikeal/request/commit/aa4a28586354901b0c9b298a0aa79abb5ed175af) Add patch convenience method. (@mloar)
- [66501b9](https://github.com/mikeal/request/commit/66501b9872abc9a2065430cd5ed4a34dd45c8bee) protect against double callback (@spollack)
- [#444](https://github.com/mikeal/request/pull/444) protect against double callbacks on error path (@spollack)
- [#448](https://github.com/mikeal/request/pull/448) Convenience method for PATCH (@mloar)
- [6f0f8c5](https://github.com/mikeal/request/commit/6f0f8c5ee2b2fdc7118804664c2215fe9cb5a2f2) No longer doing bundle dependencies (@mikeal)
- [3997f98](https://github.com/mikeal/request/commit/3997f980722241c18454a00aeeda07d701c27a8f) No longer using bundle dependencies (@mikeal)
- [cba36ce](https://github.com/mikeal/request/commit/cba36ce64e68bd26e230b65f81256776ac66e686) Adding hawk signing to request. (@mikeal)
- [c7a8be6](https://github.com/mikeal/request/commit/c7a8be6d174eff05a9cb2fda987979e475d8543f) Fixing bug in empty options. (@mikeal)
- [67d753f](https://github.com/mikeal/request/commit/67d753fec99fa1f5a3b35ec0bbbc98896418d86c) node-uuid is much better. (@mikeal)
- [337718b](https://github.com/mikeal/request/commit/337718baa08cafb3e706d275fd7344a3c92363bb) Smarter test runner. (@mikeal)
- [bcc33ac](https://github.com/mikeal/request/commit/bcc33aca57baf6fe2a81fbf5983048c9220c71b1) Moved the cookie jar in to it's own module. (@mikeal)
- [3261be4](https://github.com/mikeal/request/commit/3261be4b5d6f45f62b9f50bec18af770cbb70957) Put aws signing in its own package. (@mikeal)
- [fbed723](https://github.com/mikeal/request/commit/fbed7234d7b532813105efdc4c54777396a6773b) OAuth signing is now in its own library. (@mikeal)
- [ef5ab90](https://github.com/mikeal/request/commit/ef5ab90277fb00d0e8eb1c565b0f6ef8c52601d3) Forever agent is now it's own package. (@mikeal)
- [ca1ed81](https://github.com/mikeal/request/commit/ca1ed813c62c7493dc77108b3efc907cc36930cb) tunneling agent is now it's own library. (@mikeal)
- [5c75621](https://github.com/mikeal/request/commit/5c75621ba5cea18bcf114117112121d361e5f3c9) Moving from main.js to index. cause it's not 2010 anymore. (@mikeal)
- [#413](https://github.com/mikeal/request/pull/413) rename googledoodle.png to .jpg (@nfriedly)
- [b4c4c28](https://github.com/mikeal/request/commit/b4c4c28424d906cd96a2131010b21d7facf8b666) Merge branch 'master' of github.com:mikeal/request (@nrn)
- [#310](https://github.com/mikeal/request/pull/310) Twitter Oauth Stuff Out of Date; Now Updated (@joemccann)
- [8b0e7e8](https://github.com/mikeal/request/commit/8b0e7e8c9d196d7286d1563aa54affcc4c8b0e1d) Comment to explain init() and start(). (@mikeal)
- [43d578d](https://github.com/mikeal/request/commit/43d578dc0206388eeae9584f540d550a06308fc8) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [b7c5ed4](https://github.com/mikeal/request/commit/b7c5ed48b618f71f138f9f08f8d705336f907e01) destroy the response if present when destroying the request (@mafintosh)
- [b279277](https://github.com/mikeal/request/commit/b279277dc2fb4b649640322980315d74db0d13f3) response.abort should be response.destroy (@mafintosh)
- [#454](https://github.com/mikeal/request/pull/454) Destroy the response if present when destroying the request (clean merge) (@mafintosh)
- [#429](https://github.com/mikeal/request/pull/429) Copy options before adding callback. (@nrn)
- [e0e0fb4](https://github.com/mikeal/request/commit/e0e0fb451f17945a02203639e4836aa327b4e30b) hawk 0.9.0 (@hueniverse)
- [#456](https://github.com/mikeal/request/pull/456) hawk 0.9.0 (@hueniverse)
- [2f60bc2](https://github.com/mikeal/request/commit/2f60bc253ff6e28df58a33da24b710b6d506849f) Fixes #453 (@mikeal)
- [805b6e4](https://github.com/mikeal/request/commit/805b6e4fe3afeeb407b4fca2e34e9caabe30f747) Fixing hawk README to match new usage. (@mikeal)
- [8feb957](https://github.com/mikeal/request/commit/8feb957911083bce552d1898b7ffcaa87104cd21) Removing old logref code. (@mikeal)
- [fcf6d67](https://github.com/mikeal/request/commit/fcf6d6765247a2645a233d95468ade2960294074) Safe stringify. (@mikeal)
- [62455bc](https://github.com/mikeal/request/commit/62455bca81e8760f25a2bf1dec2b06c8e915de79) hawk 0.10 (@hueniverse)
- [c361b41](https://github.com/mikeal/request/commit/c361b4140e7e6e4fe2a8f039951b65d54af65f42) hawk 0.10 (@hueniverse)
- [fa1ef30](https://github.com/mikeal/request/commit/fa1ef30dcdac83b271ce38c71975df0ed96b08f7) Strip the UTF8 BOM from a UTF encoded response (@kppullin)
- [9d636c0](https://github.com/mikeal/request/commit/9d636c0b3e882742e15ba989d0c2413f95364680) if query params are empty, then request path shouldn't end with a '?' (@jaipandya)
- [#462](https://github.com/mikeal/request/pull/462) if query params are empty, then request path shouldn't end with a '?' (merges cleanly now) (@jaipandya)
- [#460](https://github.com/mikeal/request/pull/460) hawk 0.10.0 (@hueniverse)
- [#461](https://github.com/mikeal/request/pull/461) Strip the UTF8 BOM from a UTF encoded response (@kppullin)
- [6d29ed7](https://github.com/mikeal/request/commit/6d29ed72e34f3b2b6d8a5cfadd96dd26b3dd246d) Moving response handlers to onResponse. (@mikeal)
- [885d6eb](https://github.com/mikeal/request/commit/885d6ebeb6130c2ab7624304f4a01a898573390b) Using querystring library from visionmedia (@kbackowski)
- [#471](https://github.com/mikeal/request/pull/471) Using querystring library from visionmedia (@kbackowski)
- [346bb42](https://github.com/mikeal/request/commit/346bb42898c5804576d9e9b3adf40123260bf73b) On strictSSL set rejectUnauthorized. (@mikeal)
- [8a45365](https://github.com/mikeal/request/commit/8a453656a705d2fa98fbf9092b1600d2ddadbb5a) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [32cfd3c](https://github.com/mikeal/request/commit/32cfd3cf7b3f23c2b1d36c5ccb475cbb3a4693ff) Style changes. (@mikeal)
- [ec07ee2](https://github.com/mikeal/request/commit/ec07ee2d3eeb90b6d0ad9f6d7f3a36da72276841) Print debug logs NODE_DEBUG=request in environment (@isaacs)
- [681af64](https://github.com/mikeal/request/commit/681af644a2ebccad8bcccb75984f7f10f909b382) Flow data in v0.10-style streams (@isaacs)
- [#473](https://github.com/mikeal/request/pull/473) V0.10 compat (@isaacs)
- [f07a8ba](https://github.com/mikeal/request/commit/f07a8baebf7001addbc0f7d7c869adddc21768ce) Release. (@mikeal)
- [1f947a1](https://github.com/mikeal/request/commit/1f947a1d2728147fbf4f57aa361d0bedcebfc206) Rolling master version. (@mikeal)
- [7a217bb](https://github.com/mikeal/request/commit/7a217bbdced9a05a786fe6534ab52734df342d3e) Reinstate querystring for `unescape` (@shimaore)
- [b0b4ca9](https://github.com/mikeal/request/commit/b0b4ca913e119337e9313a157eee2f08f77ddc38) Test for `unescape` (@shimaore)
- [#475](https://github.com/mikeal/request/pull/475) Use `unescape` from `querystring` (@shimaore)
- [28fc741](https://github.com/mikeal/request/commit/28fc741fa958a9783031189964ef6f6d7e3f3264) Release. (@mikeal)
- [d3e28ef](https://github.com/mikeal/request/commit/d3e28ef7144da4d9f22f8fb475bd5aa6a80fb947) Rolling master version. (@mikeal)
- [8f8bb9e](https://github.com/mikeal/request/commit/8f8bb9ee8c4dcd9eb815249fbe2a7cf54f61b56f) Changing so if Accept header is explicitly set, sending json does not overwrite. (@RoryH)
- [#479](https://github.com/mikeal/request/pull/479) Changing so if Accept header is explicitly set, sending json does not ov... (@RoryH)
- [7694372](https://github.com/mikeal/request/commit/7694372f3dc9d57ac29ca7ee5c00146aa5e1e747) Proper version for latest. (@mikeal)
- [aa208cf](https://github.com/mikeal/request/commit/aa208cf5c682262529d749f592db147182cacfaf) 0.8+ only now (@mikeal)
- [16b5ab9](https://github.com/mikeal/request/commit/16b5ab9151823067b05b382241483ef10811c3e1) Upgrading qs. (@mikeal)
- [7d10c1e](https://github.com/mikeal/request/commit/7d10c1e83b4663f592c773e7fece83435585a06f) Merge branch 'master' of github.com:mikeal/request (@mikeal)
- [b8ca4b4](https://github.com/mikeal/request/commit/b8ca4b474b8215cab44ef8ef789303571b3d016f) pumping hawk version. (@mikeal)
- [9c0e484](https://github.com/mikeal/request/commit/9c0e48430e3a9de8715e77c07c98301399eaf6e3) release (@mikeal)
- [a9f1896](https://github.com/mikeal/request/commit/a9f189697e2a813bee9bff31de32a25e99e55cf2) rolling master version. (@mikeal)
- [560a1f8](https://github.com/mikeal/request/commit/560a1f8b927099e44b75274375a690df2a05de67) Set content-type on input. (@mikeal)
- [5fec436](https://github.com/mikeal/request/commit/5fec436b6602bc8c76133664bca23e98f511b096) Release. (@mikeal)
- [88d8d5b](https://github.com/mikeal/request/commit/88d8d5bc80679b78a39cab8e6d8295728a0a150d) Rolling version. (@mikeal)
- [d05b6ba](https://github.com/mikeal/request/commit/d05b6ba72702c2411b4627d4d89190a5f2aba562) Empty body must be passed as empty string, exclude JSON case (@Olegas)
- [#490](https://github.com/mikeal/request/pull/490) Empty response body (3-rd argument) must be passed to callback as an empty string (@Olegas)
- [8aa13cd](https://github.com/mikeal/request/commit/8aa13cd5b5e22b24466ef0e59fa8b5f1d0f0795a) Added redirect event (@Cauldrath)
- [4d63a04](https://github.com/mikeal/request/commit/4d63a042553c90718bf0b90652921b26c52dcb31) Moving response emit above setHeaders on destination streams (@kenperkins)
- [#498](https://github.com/mikeal/request/pull/498) Moving response emit above setHeaders on destination streams (@kenperkins)
- [c40993f](https://github.com/mikeal/request/commit/c40993fc987b1a8a3cb08cd5699b2f1b2bd4b28b) Fix a regression introduced by cba36ce6 (@nylen)
- [edc2e17](https://github.com/mikeal/request/commit/edc2e17e8154239efa6bd2914435798c18882635) Don't delete headers when retrying a request with proper authentication (@nylen)
- [a375ac1](https://github.com/mikeal/request/commit/a375ac15460f4f3b679f4418d7fc467a5cc94499) Refactor and expand basic auth tests (@nylen)
- [9bc28bf](https://github.com/mikeal/request/commit/9bc28bf912fb0afdd14b36b0ccbafb185a32546a) Cleanup whitespace. (@mikeal)
- [9a35cd2](https://github.com/mikeal/request/commit/9a35cd2248d9492b099c7ee46d68ca017b6a701c) Fix basic auth for passwords that contain colons (@tonistiigi)
- [f724810](https://github.com/mikeal/request/commit/f724810c7b9f82fa1423d0a4d19fcb5aaca98137) Honor the .strictSSL option when using proxies (tunnel-agent) (@jhs)
- [95a2558](https://github.com/mikeal/request/commit/95a25580375be1b9c39cc2e88a36a8387395bc13) Add HTTP Signature support. (@davidlehn)
- [921c973](https://github.com/mikeal/request/commit/921c973015721ee0f92ed670f5e88bca057104cc) * Make password optional to support the format: http://username@hostname/
- [2759ebb](https://github.com/mikeal/request/commit/2759ebbe07e8563fd3ded698d2236309fb28176b) add 'localAddress' support (@yyfrankyy)
- [#513](https://github.com/mikeal/request/pull/513) add 'localAddress' support (@yyfrankyy)
- [#512](https://github.com/mikeal/request/pull/512) Make password optional to support the format: http://username@hostname/ (@pajato1)
- [#508](https://github.com/mikeal/request/pull/508) Honor the .strictSSL option when using proxies (tunnel-agent) (@iriscouch)
- [5f036e6](https://github.com/mikeal/request/commit/5f036e6f5d3102a89e5401a53090a0627a7850a8) Conflicts: 	index.js (@nylen)
- [89d2602](https://github.com/mikeal/request/commit/89d2602ef4e3a4e6e51284f6a29b5767c79ffaba) Conflicts: 	README.md (@davidlehn)
- [#502](https://github.com/mikeal/request/pull/502) Fix POST (and probably other) requests that are retried after 401 Unauthorized (@nylen)
- [eb3e033](https://github.com/mikeal/request/commit/eb3e033170403832fe7070955db32112ec46005f) Merge branch 'master' of git://github.com/mikeal/request (@davidlehn)
- [#510](https://github.com/mikeal/request/pull/510) Add HTTP Signature support. (@digitalbazaar)
- [227d998](https://github.com/mikeal/request/commit/227d9985426214b6ac68702933346000298d7790) Update the internal path variable when querystring is changed (@jblebrun)
- [#519](https://github.com/mikeal/request/pull/519) Update internal path state on post-creation QS changes (@incredible-labs)
- [428b9c1](https://github.com/mikeal/request/commit/428b9c1ad9831b7dfd6cec4ce68df358590c6d65) Fixing test-tunnel.js (@noway421)
- [2417599](https://github.com/mikeal/request/commit/24175993f6c362f7fca5965feb0a11756f00baf3) Improving test-localAddress.js (@noway421)
- [#520](https://github.com/mikeal/request/pull/520) Fixing test-tunnel.js (@noway421)
- [1e37f1b](https://github.com/mikeal/request/commit/1e37f1bea45174e09e6450bc71dfc081c8cd94de) Some explaining comments (@noway421)
- [909b024](https://github.com/mikeal/request/commit/909b024619c9e47f615749661d610cccd8421d80) Updating dependencies (@noway421)
- [#523](https://github.com/mikeal/request/pull/523) Updating dependencies (@noway421)
- [47191e1](https://github.com/mikeal/request/commit/47191e1a5e29714fb0c5f8b2162b2971570df644) 2.17.0 (@mikeal)
- [14def5a](https://github.com/mikeal/request/commit/14def5af5903d03f66bd6c9be534e6b76f47c063) 2.18.0 (@mikeal)
- [56fd6b7](https://github.com/mikeal/request/commit/56fd6b7ec6da162894df0809126d688f30900d25) 2.18.1 (@mikeal)
- [37dd689](https://github.com/mikeal/request/commit/37dd68989670f8937b537579a4299d9649b8aa16) Fixing dep. (@mikeal)
- [dd7209a](https://github.com/mikeal/request/commit/dd7209a84dd40afe87db31c6ab66885e2015cb8f) 2.19.0 (@mikeal)
- [62f3b92](https://github.com/mikeal/request/commit/62f3b9203690d4ad34486fc506fc78a1c9971e03) 2.19.1 (@mikeal)
- [74c6b2e](https://github.com/mikeal/request/commit/74c6b2e315872980ee9a9a000d25e724138f28b1) Adding test for onelineproxy. (@mikeal)
- [2a01cc0](https://github.com/mikeal/request/commit/2a01cc082f544647f7176a992e02668519a694be) Fixing onelineproxy. (@mikeal)
- [8b4c920](https://github.com/mikeal/request/commit/8b4c9203adb372f2ee99b1b012406b482b27c68d) 2.20.0 (@mikeal)
- [d8d4a33](https://github.com/mikeal/request/commit/d8d4a3311d8d31df88fa8a2ab3265872e5cb97ae) 2.20.1 (@mikeal)
- [5937012](https://github.com/mikeal/request/commit/59370123b22e8c971e4ee48c3d0caf920d890bda) dependencies versions bump (@jodaka)
- [#529](https://github.com/mikeal/request/pull/529) dependencies versions bump (@jodaka)
- [#521](https://github.com/mikeal/request/pull/521) Improving test-localAddress.js (@noway421)
- [#503](https://github.com/mikeal/request/pull/503) Fix basic auth for passwords that contain colons (@tonistiigi)
- [#497](https://github.com/mikeal/request/pull/497) Added redirect event (@Cauldrath)
- [297a9ea](https://github.com/mikeal/request/commit/297a9ea827655e5fb406a86907bb0d89b01deae8) fix typo (@fredericosilva)
- [#532](https://github.com/mikeal/request/pull/532) fix typo (@fredericosilva)
- [3691db5](https://github.com/mikeal/request/commit/3691db5a2d0981d4aeabfda5b988a5c69074e187) Allow explicitly empty user field for basic authentication. (@mikeando)
- [#536](https://github.com/mikeal/request/pull/536) Allow explicitly empty user field for basic authentication. (@mikeando)
- [5d36e32](https://github.com/mikeal/request/commit/5d36e324047f79cbbf3bb9b71fef633f02b36367) 2.21.0 (@mikeal)
- [9bd98d6](https://github.com/mikeal/request/commit/9bd98d6052f222aa348635c1acb2e2c99eed0f8c) 2.21.1 (@mikeal)
- [a918e04](https://github.com/mikeal/request/commit/a918e04a8d767a2948567ea29ed3fdd1650c16b1) The exported request function doesn't have an auth method (@tschaub)
- [1ebe1ac](https://github.com/mikeal/request/commit/1ebe1ac2f78e8a6149c03ce68fcb23d56df2316e) exposing Request class (@regality)
- [#542](https://github.com/mikeal/request/pull/542) Expose Request class (@ifit)
- [467573d](https://github.com/mikeal/request/commit/467573d17b4db5f93ed425ace0594370a7820c7c) Update http-signatures version. (@davidlehn)
- [#541](https://github.com/mikeal/request/pull/541) The exported request function doesn't have an auth method (@tschaub)
- [3040bbe](https://github.com/mikeal/request/commit/3040bbe5de846811151dab8dc09944acc93a338e) Fix redirections, (@criloz)
- [#564](https://github.com/mikeal/request/pull/564) Fix redirections (@NebTex)
- [397b435](https://github.com/mikeal/request/commit/397b4350fcf885460d7dced94cf1db1f5c167f80) handle ciphers and secureOptions in agentOptions (@SamPlacette)
- [65a2778](https://github.com/mikeal/request/commit/65a27782db7d2798b6490ea08efacb8f3b0a401c) tests and fix for null agentOptions case (@SamPlacette)
- [#568](https://github.com/mikeal/request/pull/568) use agentOptions to create agent when specified in request (@SamPlacette)
- [c116920](https://github.com/mikeal/request/commit/c116920a2cbef25afe2e1bbcf4df074e1e2f9dbb) Let's see how we do with only the main guard. (@mikeal)
- [f54a335](https://github.com/mikeal/request/commit/f54a3358119298634a7b0c29a21bf1471fc23d98) Fix spelling of "ignoring." (@bigeasy)
- [5cd215f](https://github.com/mikeal/request/commit/5cd215f327e113dc6c062634e405c577986cfd3c) Change isUrl regex to accept mixed case (@lexander)
- [02c8e74](https://github.com/mikeal/request/commit/02c8e749360a47d45e3e7b51b7f751fe498d2f25) #583 added tests for isUrl regex change. (@lexander)
- [#581](https://github.com/mikeal/request/pull/581) Fix spelling of "ignoring." (@bigeasy)
- [#544](https://github.com/mikeal/request/pull/544) Update http-signature version. (@digitalbazaar)
- [e77746b](https://github.com/mikeal/request/commit/e77746bf42e974dc91a84d03f44f750dd7ee0989) global cookie jar disabled by default, send jar: true to enable. (@threepointone)
- [46015ac](https://github.com/mikeal/request/commit/46015ac8d5b74f8107a6ec9fd07c133f46c5d833) 2.22.0 (@mikeal)
- [e5da4a5](https://github.com/mikeal/request/commit/e5da4a5e1a20bf4f23681f7b996f22c5fadae91d) 2.22.1 (@mikeal)
- [#587](https://github.com/mikeal/request/pull/587) Global cookie jar disabled by default (@threepointone)
- [fac9da1](https://github.com/mikeal/request/commit/fac9da1cc426bf0a4bcc5f0b7d0d0aea8b1cce38) Prevent setting headers after they are sent (@wpreul)
- [#589](https://github.com/mikeal/request/pull/589) Prevent setting headers after they are sent (@wpreul)
- [bc1537a](https://github.com/mikeal/request/commit/bc1537ab79064cea532b0d14110ce4e49a663bde) Emit complete event when there is no callback
- [de8508e](https://github.com/mikeal/request/commit/de8508e9feac10563596aeee26727567b3c2e33c) Added check to see if the global pool is being used before using the global agent (@Cauldrath)
- [03441ef](https://github.com/mikeal/request/commit/03441ef919e51a742aaf9e168d917e97e2d9eb6b) 2.23.0 (@mikeal)
