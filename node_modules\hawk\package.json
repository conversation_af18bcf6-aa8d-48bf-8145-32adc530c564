{"name": "hawk", "description": "HTTP Hawk Authentication Scheme", "version": "1.1.1", "author": "<PERSON><PERSON> <<EMAIL>> (http://hueniverse.com)", "contributors": [], "repository": "git://github.com/hueniverse/hawk", "main": "index", "keywords": ["http", "authentication", "scheme", "hawk"], "engines": {"node": ">=0.8.0"}, "dependencies": {"hoek": "0.9.x", "boom": "0.4.x", "cryptiles": "0.2.x", "sntp": "0.2.x"}, "devDependencies": {"lab": "0.1.x", "complexity-report": "0.x.x", "localStorage": "1.0.x"}, "scripts": {"test": "make test-cov"}, "licenses": [{"type": "BSD", "url": "http://github.com/hueniverse/hawk/raw/master/LICENSE"}]}