console.log("Running server.js...");

const nodemailer = require('nodemailer');

const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
        user: '<EMAIL>',
        pass: 'wuou jvmd edtl ybgf'  // ✅ App Password from Google
    }
});

const mailOptions = {
    from: '<EMAIL>',          // ✅ Must match the user email above
    to: '<EMAIL>',        // ✅ Recipient's email
    subject: 'Test Email from Node.js',
    text: 'Hello! This is a test email sent using Nodemailer and Gmail.'
};

transporter.sendMail(mailOptions, (error, info) => {
    if (error) {
        console.log('❌ Error occurred:', error);
    } else {
        console.log('✅ Email sent successfully:', info.response);
    }
});
