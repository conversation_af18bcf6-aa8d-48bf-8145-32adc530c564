{"author": "<PERSON> <<EMAIL>> (http://debuggable.com/)", "name": "combined-stream", "description": "A stream that emits multiple other streams one after another.", "version": "0.0.7", "homepage": "https://github.com/felixge/node-combined-stream", "repository": {"type": "git", "url": "git://github.com/felixge/node-combined-stream.git"}, "main": "./lib/combined_stream", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"delayed-stream": "0.0.5"}, "devDependencies": {"far": "~0.0.7"}}