{"author": "<PERSON> <<EMAIL>> (http://debuggable.com/)", "name": "form-data", "description": "A module to create readable \"multipart/form-data\" streams.  Can be used to submit forms and file uploads to other web applications.", "version": "0.1.4", "repository": {"type": "git", "url": "git://github.com/felixge/node-form-data.git"}, "main": "./lib/form_data", "scripts": {"test": "node test/run.js"}, "engines": {"node": ">= 0.8"}, "dependencies": {"combined-stream": "~0.0.4", "mime": "~1.2.11", "async": "~0.9.0"}, "licenses": [{"type": "MIT", "url": "https://raw.github.com/felixge/node-form-data/master/License"}], "devDependencies": {"fake": "~0.2.2", "far": "~0.0.7", "formidable": "~1.0.14", "request": "~2.36.0"}}