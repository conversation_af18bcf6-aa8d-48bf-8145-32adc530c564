{"name": "qs", "version": "1.2.2", "description": "A querystring parser that supports nesting and arrays, with a depth limit", "homepage": "https://github.com/hapijs/qs", "main": "index.js", "dependencies": {}, "devDependencies": {"lab": "3.x.x"}, "scripts": {"test": "make test-cov"}, "repository": {"type": "git", "url": "https://github.com/hapijs/qs.git"}, "keywords": ["querystring", "qs"], "author": "<PERSON> <<EMAIL>>", "licenses": [{"type": "BSD", "url": "http://github.com/hapijs/qs/raw/master/LICENSE"}]}